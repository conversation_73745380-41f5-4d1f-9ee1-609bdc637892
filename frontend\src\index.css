/* Estilos globais para o aplicativo */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #3b82f6;
  --primary-hover: #2563eb;
  --secondary: #64748b;
  --background: #f8fafc;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --radius: 0.5rem;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Estilos específicos para o simulado */
.question-card {
  transition: transform 0.2s ease-in-out;
}

.question-card:hover {
  transform: translateY(-2px);
}

.progress-bar {
  height: 8px;
  border-radius: 4px;
  background-color: #e2e8f0;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary);
  transition: width 0.3s ease-in-out;
}

.option {
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.option:hover {
  background-color: #f1f5f9;
}

.option.selected {
  border-color: var(--primary);
  background-color: #eff6ff;
}

.option.correct {
  border-color: #22c55e;
  background-color: #f0fdf4;
}

.option.incorrect {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* Responsividade */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* Acessibilidade */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Tema escuro (opcional) */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
    --card: #1e293b;
    --card-foreground: #f8fafc;
    --border: #334155;
    --input: #334155;
  }
  
  .option:hover {
    background-color: #1e293b;
  }
  
  .option.selected {
    background-color: #172554;
  }
  
  .option.correct {
    background-color: #14532d;
  }
  
  .option.incorrect {
    background-color: #7f1d1d;
  }
}
