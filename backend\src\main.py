import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))  # DON'T CHANGE THIS !!!

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import json
import random

app = Flask(__name__, static_folder='static')
CORS(app)  # Habilita CORS para todas as rotas

# Carrega as questões do arquivo JSON
def load_questions():
    try:
        with open(os.path.join(os.path.dirname(__file__), 'data', 'questions.json'), 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Erro ao carregar questões: {e}")
        return {"questoes": [], "categorias": [], "simulados": []}

# Rotas da <PERSON>
@app.route('/api/questions', methods=['GET'])
def get_questions():
    data = load_questions()
    return jsonify(data)

@app.route('/api/questions/category/<category>', methods=['GET'])
def get_questions_by_category(category):
    data = load_questions()
    filtered_questions = [q for q in data['questoes'] if q['categoria'] == category]
    return jsonify(filtered_questions)

@app.route('/api/questions/simulado/<simulado>', methods=['GET'])
def get_questions_by_simulado(simulado):
    data = load_questions()
    filtered_questions = [q for q in data['questoes'] if simulado in q['simulados']]
    return jsonify(filtered_questions)

@app.route('/api/questions/random/<int:count>', methods=['GET'])
def get_random_questions(count):
    data = load_questions()
    if count > len(data['questoes']):
        count = len(data['questoes'])
    random_questions = random.sample(data['questoes'], count)
    return jsonify(random_questions)

@app.route('/api/categories', methods=['GET'])
def get_categories():
    data = load_questions()
    return jsonify(data['categorias'])

@app.route('/api/simulados', methods=['GET'])
def get_simulados():
    data = load_questions()
    return jsonify(data['simulados'])

# Rota para servir o frontend
@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    if path != "" and os.path.exists(app.static_folder + '/' + path):
        return send_from_directory(app.static_folder, path)
    else:
        return send_from_directory(app.static_folder, 'index.html')

if __name__ == '__main__':
    # Certifique-se de que o diretório de dados existe
    os.makedirs(os.path.join(os.path.dirname(__file__), 'data'), exist_ok=True)
    
    # Verifica se o arquivo de questões existe, se não, cria um arquivo vazio
    questions_file = os.path.join(os.path.dirname(__file__), 'data', 'questions.json')
    if not os.path.exists(questions_file):
        with open(questions_file, 'w', encoding='utf-8') as f:
            json.dump({"questoes": [], "categorias": [], "simulados": []}, f, ensure_ascii=False, indent=2)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
