function bh(i,o){for(var s=0;s<o.length;s++){const l=o[s];if(typeof l!="string"&&!Array.isArray(l)){for(const a in l)if(a!=="default"&&!(a in i)){const f=Object.getOwnPropertyDescriptor(l,a);f&&Object.defineProperty(i,a,f.get?f:{enumerable:!0,get:()=>l[a]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const f of a)if(f.type==="childList")for(const p of f.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&l(p)}).observe(document,{childList:!0,subtree:!0});function s(a){const f={};return a.integrity&&(f.integrity=a.integrity),a.referrerPolicy&&(f.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?f.credentials="include":a.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function l(a){if(a.ep)return;a.ep=!0;const f=s(a);fetch(a.href,f)}})();function Qf(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Dl={exports:{}},Kr={},Ml={exports:{}},se={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sf;function Vh(){if(sf)return se;sf=1;var i=Symbol.for("react.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),p=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),E=Symbol.iterator;function F(x){return x===null||typeof x!="object"?null:(x=E&&x[E]||x["@@iterator"],typeof x=="function"?x:null)}var j={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,A={};function N(x,k,K){this.props=x,this.context=k,this.refs=A,this.updater=K||j}N.prototype.isReactComponent={},N.prototype.setState=function(x,k){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,k,"setState")},N.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function $(){}$.prototype=N.prototype;function q(x,k,K){this.props=x,this.context=k,this.refs=A,this.updater=K||j}var z=q.prototype=new $;z.constructor=q,T(z,N.prototype),z.isPureReactComponent=!0;var b=Array.isArray,ie=Object.prototype.hasOwnProperty,Z={current:null},ue={key:!0,ref:!0,__self:!0,__source:!0};function le(x,k,K){var W,re={},de=null,ge=null;if(k!=null)for(W in k.ref!==void 0&&(ge=k.ref),k.key!==void 0&&(de=""+k.key),k)ie.call(k,W)&&!ue.hasOwnProperty(W)&&(re[W]=k[W]);var me=arguments.length-2;if(me===1)re.children=K;else if(1<me){for(var Ee=Array(me),ut=0;ut<me;ut++)Ee[ut]=arguments[ut+2];re.children=Ee}if(x&&x.defaultProps)for(W in me=x.defaultProps,me)re[W]===void 0&&(re[W]=me[W]);return{$$typeof:i,type:x,key:de,ref:ge,props:re,_owner:Z.current}}function De(x,k){return{$$typeof:i,type:x.type,key:k,ref:x.ref,props:x.props,_owner:x._owner}}function ye(x){return typeof x=="object"&&x!==null&&x.$$typeof===i}function Qe(x){var k={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(K){return k[K]})}var Te=/\/+/g;function Se(x,k){return typeof x=="object"&&x!==null&&x.key!=null?Qe(""+x.key):k.toString(36)}function _e(x,k,K,W,re){var de=typeof x;(de==="undefined"||de==="boolean")&&(x=null);var ge=!1;if(x===null)ge=!0;else switch(de){case"string":case"number":ge=!0;break;case"object":switch(x.$$typeof){case i:case o:ge=!0}}if(ge)return ge=x,re=re(ge),x=W===""?"."+Se(ge,0):W,b(re)?(K="",x!=null&&(K=x.replace(Te,"$&/")+"/"),_e(re,k,K,"",function(ut){return ut})):re!=null&&(ye(re)&&(re=De(re,K+(!re.key||ge&&ge.key===re.key?"":(""+re.key).replace(Te,"$&/")+"/")+x)),k.push(re)),1;if(ge=0,W=W===""?".":W+":",b(x))for(var me=0;me<x.length;me++){de=x[me];var Ee=W+Se(de,me);ge+=_e(de,k,K,Ee,re)}else if(Ee=F(x),typeof Ee=="function")for(x=Ee.call(x),me=0;!(de=x.next()).done;)de=de.value,Ee=W+Se(de,me++),ge+=_e(de,k,K,Ee,re);else if(de==="object")throw k=String(x),Error("Objects are not valid as a React child (found: "+(k==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":k)+"). If you meant to render a collection of children, use an array instead.");return ge}function Fe(x,k,K){if(x==null)return x;var W=[],re=0;return _e(x,W,"","",function(de){return k.call(K,de,re++)}),W}function ae(x){if(x._status===-1){var k=x._result;k=k(),k.then(function(K){(x._status===0||x._status===-1)&&(x._status=1,x._result=K)},function(K){(x._status===0||x._status===-1)&&(x._status=2,x._result=K)}),x._status===-1&&(x._status=0,x._result=k)}if(x._status===1)return x._result.default;throw x._result}var ce={current:null},Q={transition:null},X={ReactCurrentDispatcher:ce,ReactCurrentBatchConfig:Q,ReactCurrentOwner:Z};function B(){throw Error("act(...) is not supported in production builds of React.")}return se.Children={map:Fe,forEach:function(x,k,K){Fe(x,function(){k.apply(this,arguments)},K)},count:function(x){var k=0;return Fe(x,function(){k++}),k},toArray:function(x){return Fe(x,function(k){return k})||[]},only:function(x){if(!ye(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},se.Component=N,se.Fragment=s,se.Profiler=a,se.PureComponent=q,se.StrictMode=l,se.Suspense=y,se.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=X,se.act=B,se.cloneElement=function(x,k,K){if(x==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+x+".");var W=T({},x.props),re=x.key,de=x.ref,ge=x._owner;if(k!=null){if(k.ref!==void 0&&(de=k.ref,ge=Z.current),k.key!==void 0&&(re=""+k.key),x.type&&x.type.defaultProps)var me=x.type.defaultProps;for(Ee in k)ie.call(k,Ee)&&!ue.hasOwnProperty(Ee)&&(W[Ee]=k[Ee]===void 0&&me!==void 0?me[Ee]:k[Ee])}var Ee=arguments.length-2;if(Ee===1)W.children=K;else if(1<Ee){me=Array(Ee);for(var ut=0;ut<Ee;ut++)me[ut]=arguments[ut+2];W.children=me}return{$$typeof:i,type:x.type,key:re,ref:de,props:W,_owner:ge}},se.createContext=function(x){return x={$$typeof:p,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},x.Provider={$$typeof:f,_context:x},x.Consumer=x},se.createElement=le,se.createFactory=function(x){var k=le.bind(null,x);return k.type=x,k},se.createRef=function(){return{current:null}},se.forwardRef=function(x){return{$$typeof:h,render:x}},se.isValidElement=ye,se.lazy=function(x){return{$$typeof:w,_payload:{_status:-1,_result:x},_init:ae}},se.memo=function(x,k){return{$$typeof:v,type:x,compare:k===void 0?null:k}},se.startTransition=function(x){var k=Q.transition;Q.transition={};try{x()}finally{Q.transition=k}},se.unstable_act=B,se.useCallback=function(x,k){return ce.current.useCallback(x,k)},se.useContext=function(x){return ce.current.useContext(x)},se.useDebugValue=function(){},se.useDeferredValue=function(x){return ce.current.useDeferredValue(x)},se.useEffect=function(x,k){return ce.current.useEffect(x,k)},se.useId=function(){return ce.current.useId()},se.useImperativeHandle=function(x,k,K){return ce.current.useImperativeHandle(x,k,K)},se.useInsertionEffect=function(x,k){return ce.current.useInsertionEffect(x,k)},se.useLayoutEffect=function(x,k){return ce.current.useLayoutEffect(x,k)},se.useMemo=function(x,k){return ce.current.useMemo(x,k)},se.useReducer=function(x,k,K){return ce.current.useReducer(x,k,K)},se.useRef=function(x){return ce.current.useRef(x)},se.useState=function(x){return ce.current.useState(x)},se.useSyncExternalStore=function(x,k,K){return ce.current.useSyncExternalStore(x,k,K)},se.useTransition=function(){return ce.current.useTransition()},se.version="18.3.1",se}var lf;function ru(){return lf||(lf=1,Ml.exports=Vh()),Ml.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uf;function Hh(){if(uf)return Kr;uf=1;var i=ru(),o=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(h,y,v){var w,E={},F=null,j=null;v!==void 0&&(F=""+v),y.key!==void 0&&(F=""+y.key),y.ref!==void 0&&(j=y.ref);for(w in y)l.call(y,w)&&!f.hasOwnProperty(w)&&(E[w]=y[w]);if(h&&h.defaultProps)for(w in y=h.defaultProps,y)E[w]===void 0&&(E[w]=y[w]);return{$$typeof:o,type:h,key:F,ref:j,props:E,_owner:a.current}}return Kr.Fragment=s,Kr.jsx=p,Kr.jsxs=p,Kr}var af;function Kh(){return af||(af=1,Dl.exports=Hh()),Dl.exports}var _=Kh(),U=ru();const Ne=Qf(U),$f=bh({__proto__:null,default:Ne},[U]);var po={},Ul={exports:{}},ot={},zl={exports:{}},Ql={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cf;function Wh(){return cf||(cf=1,function(i){function o(Q,X){var B=Q.length;Q.push(X);e:for(;0<B;){var x=B-1>>>1,k=Q[x];if(0<a(k,X))Q[x]=X,Q[B]=k,B=x;else break e}}function s(Q){return Q.length===0?null:Q[0]}function l(Q){if(Q.length===0)return null;var X=Q[0],B=Q.pop();if(B!==X){Q[0]=B;e:for(var x=0,k=Q.length,K=k>>>1;x<K;){var W=2*(x+1)-1,re=Q[W],de=W+1,ge=Q[de];if(0>a(re,B))de<k&&0>a(ge,re)?(Q[x]=ge,Q[de]=B,x=de):(Q[x]=re,Q[W]=B,x=W);else if(de<k&&0>a(ge,B))Q[x]=ge,Q[de]=B,x=de;else break e}}return X}function a(Q,X){var B=Q.sortIndex-X.sortIndex;return B!==0?B:Q.id-X.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;i.unstable_now=function(){return f.now()}}else{var p=Date,h=p.now();i.unstable_now=function(){return p.now()-h}}var y=[],v=[],w=1,E=null,F=3,j=!1,T=!1,A=!1,N=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function z(Q){for(var X=s(v);X!==null;){if(X.callback===null)l(v);else if(X.startTime<=Q)l(v),X.sortIndex=X.expirationTime,o(y,X);else break;X=s(v)}}function b(Q){if(A=!1,z(Q),!T)if(s(y)!==null)T=!0,ae(ie);else{var X=s(v);X!==null&&ce(b,X.startTime-Q)}}function ie(Q,X){T=!1,A&&(A=!1,$(le),le=-1),j=!0;var B=F;try{for(z(X),E=s(y);E!==null&&(!(E.expirationTime>X)||Q&&!Qe());){var x=E.callback;if(typeof x=="function"){E.callback=null,F=E.priorityLevel;var k=x(E.expirationTime<=X);X=i.unstable_now(),typeof k=="function"?E.callback=k:E===s(y)&&l(y),z(X)}else l(y);E=s(y)}if(E!==null)var K=!0;else{var W=s(v);W!==null&&ce(b,W.startTime-X),K=!1}return K}finally{E=null,F=B,j=!1}}var Z=!1,ue=null,le=-1,De=5,ye=-1;function Qe(){return!(i.unstable_now()-ye<De)}function Te(){if(ue!==null){var Q=i.unstable_now();ye=Q;var X=!0;try{X=ue(!0,Q)}finally{X?Se():(Z=!1,ue=null)}}else Z=!1}var Se;if(typeof q=="function")Se=function(){q(Te)};else if(typeof MessageChannel<"u"){var _e=new MessageChannel,Fe=_e.port2;_e.port1.onmessage=Te,Se=function(){Fe.postMessage(null)}}else Se=function(){N(Te,0)};function ae(Q){ue=Q,Z||(Z=!0,Se())}function ce(Q,X){le=N(function(){Q(i.unstable_now())},X)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(Q){Q.callback=null},i.unstable_continueExecution=function(){T||j||(T=!0,ae(ie))},i.unstable_forceFrameRate=function(Q){0>Q||125<Q?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):De=0<Q?Math.floor(1e3/Q):5},i.unstable_getCurrentPriorityLevel=function(){return F},i.unstable_getFirstCallbackNode=function(){return s(y)},i.unstable_next=function(Q){switch(F){case 1:case 2:case 3:var X=3;break;default:X=F}var B=F;F=X;try{return Q()}finally{F=B}},i.unstable_pauseExecution=function(){},i.unstable_requestPaint=function(){},i.unstable_runWithPriority=function(Q,X){switch(Q){case 1:case 2:case 3:case 4:case 5:break;default:Q=3}var B=F;F=Q;try{return X()}finally{F=B}},i.unstable_scheduleCallback=function(Q,X,B){var x=i.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?x+B:x):B=x,Q){case 1:var k=-1;break;case 2:k=250;break;case 5:k=**********;break;case 4:k=1e4;break;default:k=5e3}return k=B+k,Q={id:w++,callback:X,priorityLevel:Q,startTime:B,expirationTime:k,sortIndex:-1},B>x?(Q.sortIndex=B,o(v,Q),s(y)===null&&Q===s(v)&&(A?($(le),le=-1):A=!0,ce(b,B-x))):(Q.sortIndex=k,o(y,Q),T||j||(T=!0,ae(ie))),Q},i.unstable_shouldYield=Qe,i.unstable_wrapCallback=function(Q){var X=F;return function(){var B=F;F=X;try{return Q.apply(this,arguments)}finally{F=B}}}}(Ql)),Ql}var ff;function Gh(){return ff||(ff=1,zl.exports=Wh()),zl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var df;function Jh(){if(df)return ot;df=1;var i=ru(),o=Gh();function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,a={};function f(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(a[e]=t,e=0;e<t.length;e++)l.add(t[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),y=Object.prototype.hasOwnProperty,v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},E={};function F(e){return y.call(E,e)?!0:y.call(w,e)?!1:v.test(e)?E[e]=!0:(w[e]=!0,!1)}function j(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function T(e,t,n,r){if(t===null||typeof t>"u"||j(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function A(e,t,n,r,u,c,d){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=u,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=c,this.removeEmptyString=d}var N={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){N[e]=new A(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];N[t]=new A(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){N[e]=new A(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){N[e]=new A(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){N[e]=new A(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){N[e]=new A(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){N[e]=new A(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){N[e]=new A(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){N[e]=new A(e,5,!1,e.toLowerCase(),null,!1,!1)});var $=/[\-:]([a-z])/g;function q(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace($,q);N[t]=new A(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace($,q);N[t]=new A(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace($,q);N[t]=new A(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){N[e]=new A(e,1,!1,e.toLowerCase(),null,!1,!1)}),N.xlinkHref=new A("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){N[e]=new A(e,1,!1,e.toLowerCase(),null,!0,!0)});function z(e,t,n,r){var u=N.hasOwnProperty(t)?N[t]:null;(u!==null?u.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(T(t,n,u,r)&&(n=null),r||u===null?F(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):u.mustUseProperty?e[u.propertyName]=n===null?u.type===3?!1:"":n:(t=u.attributeName,r=u.attributeNamespace,n===null?e.removeAttribute(t):(u=u.type,n=u===3||u===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var b=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ie=Symbol.for("react.element"),Z=Symbol.for("react.portal"),ue=Symbol.for("react.fragment"),le=Symbol.for("react.strict_mode"),De=Symbol.for("react.profiler"),ye=Symbol.for("react.provider"),Qe=Symbol.for("react.context"),Te=Symbol.for("react.forward_ref"),Se=Symbol.for("react.suspense"),_e=Symbol.for("react.suspense_list"),Fe=Symbol.for("react.memo"),ae=Symbol.for("react.lazy"),ce=Symbol.for("react.offscreen"),Q=Symbol.iterator;function X(e){return e===null||typeof e!="object"?null:(e=Q&&e[Q]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,x;function k(e){if(x===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);x=t&&t[1]||""}return`
`+x+e}var K=!1;function W(e,t){if(!e||K)return"";K=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(O){var r=O}Reflect.construct(e,[],t)}else{try{t.call()}catch(O){r=O}e.call(t.prototype)}else{try{throw Error()}catch(O){r=O}e()}}catch(O){if(O&&r&&typeof O.stack=="string"){for(var u=O.stack.split(`
`),c=r.stack.split(`
`),d=u.length-1,m=c.length-1;1<=d&&0<=m&&u[d]!==c[m];)m--;for(;1<=d&&0<=m;d--,m--)if(u[d]!==c[m]){if(d!==1||m!==1)do if(d--,m--,0>m||u[d]!==c[m]){var g=`
`+u[d].replace(" at new "," at ");return e.displayName&&g.includes("<anonymous>")&&(g=g.replace("<anonymous>",e.displayName)),g}while(1<=d&&0<=m);break}}}finally{K=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?k(e):""}function re(e){switch(e.tag){case 5:return k(e.type);case 16:return k("Lazy");case 13:return k("Suspense");case 19:return k("SuspenseList");case 0:case 2:case 15:return e=W(e.type,!1),e;case 11:return e=W(e.type.render,!1),e;case 1:return e=W(e.type,!0),e;default:return""}}function de(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ue:return"Fragment";case Z:return"Portal";case De:return"Profiler";case le:return"StrictMode";case Se:return"Suspense";case _e:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Qe:return(e.displayName||"Context")+".Consumer";case ye:return(e._context.displayName||"Context")+".Provider";case Te:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Fe:return t=e.displayName||null,t!==null?t:de(e.type)||"Memo";case ae:t=e._payload,e=e._init;try{return de(e(t))}catch{}}return null}function ge(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return de(t);case 8:return t===le?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function me(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ee(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function ut(e){var t=Ee(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var u=n.get,c=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return u.call(this)},set:function(d){r=""+d,c.call(this,d)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(d){r=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ni(e){e._valueTracker||(e._valueTracker=ut(e))}function fu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ee(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ri(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function qo(e,t){var n=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function du(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=me(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function pu(e,t){t=t.checked,t!=null&&z(e,"checked",t,!1)}function bo(e,t){pu(e,t);var n=me(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Vo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Vo(e,t.type,me(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function hu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Vo(e,t,n){(t!=="number"||ri(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ur=Array.isArray;function In(e,t,n,r){if(e=e.options,t){t={};for(var u=0;u<n.length;u++)t["$"+n[u]]=!0;for(n=0;n<e.length;n++)u=t.hasOwnProperty("$"+e[n].value),e[n].selected!==u&&(e[n].selected=u),u&&r&&(e[n].defaultSelected=!0)}else{for(n=""+me(n),t=null,u=0;u<e.length;u++){if(e[u].value===n){e[u].selected=!0,r&&(e[u].defaultSelected=!0);return}t!==null||e[u].disabled||(t=e[u])}t!==null&&(t.selected=!0)}}function Ho(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(s(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function mu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(s(92));if(ur(n)){if(1<n.length)throw Error(s(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:me(n)}}function vu(e,t){var n=me(t.value),r=me(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function yu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ko(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ii,wu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,u){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,u)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ii=ii||document.createElement("div"),ii.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ii.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ar(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var cr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Wd=["Webkit","ms","Moz","O"];Object.keys(cr).forEach(function(e){Wd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),cr[t]=cr[e]})});function Su(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||cr.hasOwnProperty(e)&&cr[e]?(""+t).trim():t+"px"}function xu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,u=Su(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,u):e[n]=u}}var Gd=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wo(e,t){if(t){if(Gd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(s(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(s(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(t.style!=null&&typeof t.style!="object")throw Error(s(62))}}function Go(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Jo=null;function Xo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Yo=null,Ln=null,Dn=null;function Cu(e){if(e=Ar(e)){if(typeof Yo!="function")throw Error(s(280));var t=e.stateNode;t&&(t=Oi(t),Yo(e.stateNode,e.type,t))}}function Eu(e){Ln?Dn?Dn.push(e):Dn=[e]:Ln=e}function ku(){if(Ln){var e=Ln,t=Dn;if(Dn=Ln=null,Cu(e),t)for(e=0;e<t.length;e++)Cu(t[e])}}function Ru(e,t){return e(t)}function _u(){}var Zo=!1;function Ou(e,t,n){if(Zo)return e(t,n);Zo=!0;try{return Ru(e,t,n)}finally{Zo=!1,(Ln!==null||Dn!==null)&&(_u(),ku())}}function fr(e,t){var n=e.stateNode;if(n===null)return null;var r=Oi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(s(231,t,typeof n));return n}var es=!1;if(h)try{var dr={};Object.defineProperty(dr,"passive",{get:function(){es=!0}}),window.addEventListener("test",dr,dr),window.removeEventListener("test",dr,dr)}catch{es=!1}function Jd(e,t,n,r,u,c,d,m,g){var O=Array.prototype.slice.call(arguments,3);try{t.apply(n,O)}catch(L){this.onError(L)}}var pr=!1,oi=null,si=!1,ts=null,Xd={onError:function(e){pr=!0,oi=e}};function Yd(e,t,n,r,u,c,d,m,g){pr=!1,oi=null,Jd.apply(Xd,arguments)}function Zd(e,t,n,r,u,c,d,m,g){if(Yd.apply(this,arguments),pr){if(pr){var O=oi;pr=!1,oi=null}else throw Error(s(198));si||(si=!0,ts=O)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Pu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Nu(e){if(mn(e)!==e)throw Error(s(188))}function ep(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(s(188));return t!==e?null:e}for(var n=e,r=t;;){var u=n.return;if(u===null)break;var c=u.alternate;if(c===null){if(r=u.return,r!==null){n=r;continue}break}if(u.child===c.child){for(c=u.child;c;){if(c===n)return Nu(u),e;if(c===r)return Nu(u),t;c=c.sibling}throw Error(s(188))}if(n.return!==r.return)n=u,r=c;else{for(var d=!1,m=u.child;m;){if(m===n){d=!0,n=u,r=c;break}if(m===r){d=!0,r=u,n=c;break}m=m.sibling}if(!d){for(m=c.child;m;){if(m===n){d=!0,n=c,r=u;break}if(m===r){d=!0,r=c,n=u;break}m=m.sibling}if(!d)throw Error(s(189))}}if(n.alternate!==r)throw Error(s(190))}if(n.tag!==3)throw Error(s(188));return n.stateNode.current===n?e:t}function Tu(e){return e=ep(e),e!==null?Fu(e):null}function Fu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Fu(e);if(t!==null)return t;e=e.sibling}return null}var Au=o.unstable_scheduleCallback,ju=o.unstable_cancelCallback,tp=o.unstable_shouldYield,np=o.unstable_requestPaint,Ie=o.unstable_now,rp=o.unstable_getCurrentPriorityLevel,ns=o.unstable_ImmediatePriority,Iu=o.unstable_UserBlockingPriority,li=o.unstable_NormalPriority,ip=o.unstable_LowPriority,Lu=o.unstable_IdlePriority,ui=null,Pt=null;function op(e){if(Pt&&typeof Pt.onCommitFiberRoot=="function")try{Pt.onCommitFiberRoot(ui,e,void 0,(e.current.flags&128)===128)}catch{}}var wt=Math.clz32?Math.clz32:up,sp=Math.log,lp=Math.LN2;function up(e){return e>>>=0,e===0?32:31-(sp(e)/lp|0)|0}var ai=64,ci=4194304;function hr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function fi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,u=e.suspendedLanes,c=e.pingedLanes,d=n&268435455;if(d!==0){var m=d&~u;m!==0?r=hr(m):(c&=d,c!==0&&(r=hr(c)))}else d=n&~u,d!==0?r=hr(d):c!==0&&(r=hr(c));if(r===0)return 0;if(t!==0&&t!==r&&(t&u)===0&&(u=r&-r,c=t&-t,u>=c||u===16&&(c&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-wt(t),u=1<<n,r|=e[n],t&=~u;return r}function ap(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function cp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,u=e.expirationTimes,c=e.pendingLanes;0<c;){var d=31-wt(c),m=1<<d,g=u[d];g===-1?((m&n)===0||(m&r)!==0)&&(u[d]=ap(m,t)):g<=t&&(e.expiredLanes|=m),c&=~m}}function rs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Du(){var e=ai;return ai<<=1,(ai&4194240)===0&&(ai=64),e}function is(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function mr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-wt(t),e[t]=n}function fp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var u=31-wt(n),c=1<<u;t[u]=0,r[u]=-1,e[u]=-1,n&=~c}}function os(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-wt(n),u=1<<r;u&t|e[r]&t&&(e[r]|=t),n&=~u}}var ve=0;function Mu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Uu,ss,zu,Qu,$u,ls=!1,di=[],bt=null,Vt=null,Ht=null,vr=new Map,yr=new Map,Kt=[],dp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Bu(e,t){switch(e){case"focusin":case"focusout":bt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":Ht=null;break;case"pointerover":case"pointerout":vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":yr.delete(t.pointerId)}}function gr(e,t,n,r,u,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:c,targetContainers:[u]},t!==null&&(t=Ar(t),t!==null&&ss(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,u!==null&&t.indexOf(u)===-1&&t.push(u),e)}function pp(e,t,n,r,u){switch(t){case"focusin":return bt=gr(bt,e,t,n,r,u),!0;case"dragenter":return Vt=gr(Vt,e,t,n,r,u),!0;case"mouseover":return Ht=gr(Ht,e,t,n,r,u),!0;case"pointerover":var c=u.pointerId;return vr.set(c,gr(vr.get(c)||null,e,t,n,r,u)),!0;case"gotpointercapture":return c=u.pointerId,yr.set(c,gr(yr.get(c)||null,e,t,n,r,u)),!0}return!1}function qu(e){var t=vn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Pu(n),t!==null){e.blockedOn=t,$u(e.priority,function(){zu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function pi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=as(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Jo=r,n.target.dispatchEvent(r),Jo=null}else return t=Ar(n),t!==null&&ss(t),e.blockedOn=n,!1;t.shift()}return!0}function bu(e,t,n){pi(e)&&n.delete(t)}function hp(){ls=!1,bt!==null&&pi(bt)&&(bt=null),Vt!==null&&pi(Vt)&&(Vt=null),Ht!==null&&pi(Ht)&&(Ht=null),vr.forEach(bu),yr.forEach(bu)}function wr(e,t){e.blockedOn===t&&(e.blockedOn=null,ls||(ls=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,hp)))}function Sr(e){function t(u){return wr(u,e)}if(0<di.length){wr(di[0],e);for(var n=1;n<di.length;n++){var r=di[n];r.blockedOn===e&&(r.blockedOn=null)}}for(bt!==null&&wr(bt,e),Vt!==null&&wr(Vt,e),Ht!==null&&wr(Ht,e),vr.forEach(t),yr.forEach(t),n=0;n<Kt.length;n++)r=Kt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Kt.length&&(n=Kt[0],n.blockedOn===null);)qu(n),n.blockedOn===null&&Kt.shift()}var Mn=b.ReactCurrentBatchConfig,hi=!0;function mp(e,t,n,r){var u=ve,c=Mn.transition;Mn.transition=null;try{ve=1,us(e,t,n,r)}finally{ve=u,Mn.transition=c}}function vp(e,t,n,r){var u=ve,c=Mn.transition;Mn.transition=null;try{ve=4,us(e,t,n,r)}finally{ve=u,Mn.transition=c}}function us(e,t,n,r){if(hi){var u=as(e,t,n,r);if(u===null)_s(e,t,r,mi,n),Bu(e,r);else if(pp(u,e,t,n,r))r.stopPropagation();else if(Bu(e,r),t&4&&-1<dp.indexOf(e)){for(;u!==null;){var c=Ar(u);if(c!==null&&Uu(c),c=as(e,t,n,r),c===null&&_s(e,t,r,mi,n),c===u)break;u=c}u!==null&&r.stopPropagation()}else _s(e,t,r,null,n)}}var mi=null;function as(e,t,n,r){if(mi=null,e=Xo(r),e=vn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Pu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return mi=e,null}function Vu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(rp()){case ns:return 1;case Iu:return 4;case li:case ip:return 16;case Lu:return 536870912;default:return 16}default:return 16}}var Wt=null,cs=null,vi=null;function Hu(){if(vi)return vi;var e,t=cs,n=t.length,r,u="value"in Wt?Wt.value:Wt.textContent,c=u.length;for(e=0;e<n&&t[e]===u[e];e++);var d=n-e;for(r=1;r<=d&&t[n-r]===u[c-r];r++);return vi=u.slice(e,1<r?1-r:void 0)}function yi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function gi(){return!0}function Ku(){return!1}function at(e){function t(n,r,u,c,d){this._reactName=n,this._targetInst=u,this.type=r,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(n=e[m],this[m]=n?n(c):c[m]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?gi:Ku,this.isPropagationStopped=Ku,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gi)},persist:function(){},isPersistent:gi}),t}var Un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},fs=at(Un),xr=B({},Un,{view:0,detail:0}),yp=at(xr),ds,ps,Cr,wi=B({},xr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ms,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cr&&(Cr&&e.type==="mousemove"?(ds=e.screenX-Cr.screenX,ps=e.screenY-Cr.screenY):ps=ds=0,Cr=e),ds)},movementY:function(e){return"movementY"in e?e.movementY:ps}}),Wu=at(wi),gp=B({},wi,{dataTransfer:0}),wp=at(gp),Sp=B({},xr,{relatedTarget:0}),hs=at(Sp),xp=B({},Un,{animationName:0,elapsedTime:0,pseudoElement:0}),Cp=at(xp),Ep=B({},Un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kp=at(Ep),Rp=B({},Un,{data:0}),Gu=at(Rp),_p={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Op={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Pp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Np(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Pp[e])?!!t[e]:!1}function ms(){return Np}var Tp=B({},xr,{key:function(e){if(e.key){var t=_p[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=yi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Op[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ms,charCode:function(e){return e.type==="keypress"?yi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?yi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Fp=at(Tp),Ap=B({},wi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ju=at(Ap),jp=B({},xr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ms}),Ip=at(jp),Lp=B({},Un,{propertyName:0,elapsedTime:0,pseudoElement:0}),Dp=at(Lp),Mp=B({},wi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Up=at(Mp),zp=[9,13,27,32],vs=h&&"CompositionEvent"in window,Er=null;h&&"documentMode"in document&&(Er=document.documentMode);var Qp=h&&"TextEvent"in window&&!Er,Xu=h&&(!vs||Er&&8<Er&&11>=Er),Yu=" ",Zu=!1;function ea(e,t){switch(e){case"keyup":return zp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ta(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zn=!1;function $p(e,t){switch(e){case"compositionend":return ta(t);case"keypress":return t.which!==32?null:(Zu=!0,Yu);case"textInput":return e=t.data,e===Yu&&Zu?null:e;default:return null}}function Bp(e,t){if(zn)return e==="compositionend"||!vs&&ea(e,t)?(e=Hu(),vi=cs=Wt=null,zn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xu&&t.locale!=="ko"?null:t.data;default:return null}}var qp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function na(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!qp[e.type]:t==="textarea"}function ra(e,t,n,r){Eu(r),t=ki(t,"onChange"),0<t.length&&(n=new fs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var kr=null,Rr=null;function bp(e){xa(e,0)}function Si(e){var t=bn(e);if(fu(t))return e}function Vp(e,t){if(e==="change")return t}var ia=!1;if(h){var ys;if(h){var gs="oninput"in document;if(!gs){var oa=document.createElement("div");oa.setAttribute("oninput","return;"),gs=typeof oa.oninput=="function"}ys=gs}else ys=!1;ia=ys&&(!document.documentMode||9<document.documentMode)}function sa(){kr&&(kr.detachEvent("onpropertychange",la),Rr=kr=null)}function la(e){if(e.propertyName==="value"&&Si(Rr)){var t=[];ra(t,Rr,e,Xo(e)),Ou(bp,t)}}function Hp(e,t,n){e==="focusin"?(sa(),kr=t,Rr=n,kr.attachEvent("onpropertychange",la)):e==="focusout"&&sa()}function Kp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Si(Rr)}function Wp(e,t){if(e==="click")return Si(t)}function Gp(e,t){if(e==="input"||e==="change")return Si(t)}function Jp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var St=typeof Object.is=="function"?Object.is:Jp;function _r(e,t){if(St(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var u=n[r];if(!y.call(t,u)||!St(e[u],t[u]))return!1}return!0}function ua(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function aa(e,t){var n=ua(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ua(n)}}function ca(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ca(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function fa(){for(var e=window,t=ri();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ri(e.document)}return t}function ws(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Xp(e){var t=fa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ca(n.ownerDocument.documentElement,n)){if(r!==null&&ws(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var u=n.textContent.length,c=Math.min(r.start,u);r=r.end===void 0?c:Math.min(r.end,u),!e.extend&&c>r&&(u=r,r=c,c=u),u=aa(n,c);var d=aa(n,r);u&&d&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==d.node||e.focusOffset!==d.offset)&&(t=t.createRange(),t.setStart(u.node,u.offset),e.removeAllRanges(),c>r?(e.addRange(t),e.extend(d.node,d.offset)):(t.setEnd(d.node,d.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Yp=h&&"documentMode"in document&&11>=document.documentMode,Qn=null,Ss=null,Or=null,xs=!1;function da(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;xs||Qn==null||Qn!==ri(r)||(r=Qn,"selectionStart"in r&&ws(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Or&&_r(Or,r)||(Or=r,r=ki(Ss,"onSelect"),0<r.length&&(t=new fs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Qn)))}function xi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $n={animationend:xi("Animation","AnimationEnd"),animationiteration:xi("Animation","AnimationIteration"),animationstart:xi("Animation","AnimationStart"),transitionend:xi("Transition","TransitionEnd")},Cs={},pa={};h&&(pa=document.createElement("div").style,"AnimationEvent"in window||(delete $n.animationend.animation,delete $n.animationiteration.animation,delete $n.animationstart.animation),"TransitionEvent"in window||delete $n.transitionend.transition);function Ci(e){if(Cs[e])return Cs[e];if(!$n[e])return e;var t=$n[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in pa)return Cs[e]=t[n];return e}var ha=Ci("animationend"),ma=Ci("animationiteration"),va=Ci("animationstart"),ya=Ci("transitionend"),ga=new Map,wa="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Gt(e,t){ga.set(e,t),f(t,[e])}for(var Es=0;Es<wa.length;Es++){var ks=wa[Es],Zp=ks.toLowerCase(),eh=ks[0].toUpperCase()+ks.slice(1);Gt(Zp,"on"+eh)}Gt(ha,"onAnimationEnd"),Gt(ma,"onAnimationIteration"),Gt(va,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(ya,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),th=new Set("cancel close invalid load scroll toggle".split(" ").concat(Pr));function Sa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Zd(r,t,void 0,e),e.currentTarget=null}function xa(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],u=r.event;r=r.listeners;e:{var c=void 0;if(t)for(var d=r.length-1;0<=d;d--){var m=r[d],g=m.instance,O=m.currentTarget;if(m=m.listener,g!==c&&u.isPropagationStopped())break e;Sa(u,m,O),c=g}else for(d=0;d<r.length;d++){if(m=r[d],g=m.instance,O=m.currentTarget,m=m.listener,g!==c&&u.isPropagationStopped())break e;Sa(u,m,O),c=g}}}if(si)throw e=ts,si=!1,ts=null,e}function xe(e,t){var n=t[As];n===void 0&&(n=t[As]=new Set);var r=e+"__bubble";n.has(r)||(Ca(t,e,2,!1),n.add(r))}function Rs(e,t,n){var r=0;t&&(r|=4),Ca(n,e,r,t)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function Nr(e){if(!e[Ei]){e[Ei]=!0,l.forEach(function(n){n!=="selectionchange"&&(th.has(n)||Rs(n,!1,e),Rs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ei]||(t[Ei]=!0,Rs("selectionchange",!1,t))}}function Ca(e,t,n,r){switch(Vu(t)){case 1:var u=mp;break;case 4:u=vp;break;default:u=us}n=u.bind(null,t,n,e),u=void 0,!es||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(u=!0),r?u!==void 0?e.addEventListener(t,n,{capture:!0,passive:u}):e.addEventListener(t,n,!0):u!==void 0?e.addEventListener(t,n,{passive:u}):e.addEventListener(t,n,!1)}function _s(e,t,n,r,u){var c=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var d=r.tag;if(d===3||d===4){var m=r.stateNode.containerInfo;if(m===u||m.nodeType===8&&m.parentNode===u)break;if(d===4)for(d=r.return;d!==null;){var g=d.tag;if((g===3||g===4)&&(g=d.stateNode.containerInfo,g===u||g.nodeType===8&&g.parentNode===u))return;d=d.return}for(;m!==null;){if(d=vn(m),d===null)return;if(g=d.tag,g===5||g===6){r=c=d;continue e}m=m.parentNode}}r=r.return}Ou(function(){var O=c,L=Xo(n),D=[];e:{var I=ga.get(e);if(I!==void 0){var V=fs,G=e;switch(e){case"keypress":if(yi(n)===0)break e;case"keydown":case"keyup":V=Fp;break;case"focusin":G="focus",V=hs;break;case"focusout":G="blur",V=hs;break;case"beforeblur":case"afterblur":V=hs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":V=Wu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":V=wp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":V=Ip;break;case ha:case ma:case va:V=Cp;break;case ya:V=Dp;break;case"scroll":V=yp;break;case"wheel":V=Up;break;case"copy":case"cut":case"paste":V=kp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":V=Ju}var J=(t&4)!==0,Le=!J&&e==="scroll",C=J?I!==null?I+"Capture":null:I;J=[];for(var S=O,R;S!==null;){R=S;var M=R.stateNode;if(R.tag===5&&M!==null&&(R=M,C!==null&&(M=fr(S,C),M!=null&&J.push(Tr(S,M,R)))),Le)break;S=S.return}0<J.length&&(I=new V(I,G,null,n,L),D.push({event:I,listeners:J}))}}if((t&7)===0){e:{if(I=e==="mouseover"||e==="pointerover",V=e==="mouseout"||e==="pointerout",I&&n!==Jo&&(G=n.relatedTarget||n.fromElement)&&(vn(G)||G[Lt]))break e;if((V||I)&&(I=L.window===L?L:(I=L.ownerDocument)?I.defaultView||I.parentWindow:window,V?(G=n.relatedTarget||n.toElement,V=O,G=G?vn(G):null,G!==null&&(Le=mn(G),G!==Le||G.tag!==5&&G.tag!==6)&&(G=null)):(V=null,G=O),V!==G)){if(J=Wu,M="onMouseLeave",C="onMouseEnter",S="mouse",(e==="pointerout"||e==="pointerover")&&(J=Ju,M="onPointerLeave",C="onPointerEnter",S="pointer"),Le=V==null?I:bn(V),R=G==null?I:bn(G),I=new J(M,S+"leave",V,n,L),I.target=Le,I.relatedTarget=R,M=null,vn(L)===O&&(J=new J(C,S+"enter",G,n,L),J.target=R,J.relatedTarget=Le,M=J),Le=M,V&&G)t:{for(J=V,C=G,S=0,R=J;R;R=Bn(R))S++;for(R=0,M=C;M;M=Bn(M))R++;for(;0<S-R;)J=Bn(J),S--;for(;0<R-S;)C=Bn(C),R--;for(;S--;){if(J===C||C!==null&&J===C.alternate)break t;J=Bn(J),C=Bn(C)}J=null}else J=null;V!==null&&Ea(D,I,V,J,!1),G!==null&&Le!==null&&Ea(D,Le,G,J,!0)}}e:{if(I=O?bn(O):window,V=I.nodeName&&I.nodeName.toLowerCase(),V==="select"||V==="input"&&I.type==="file")var Y=Vp;else if(na(I))if(ia)Y=Gp;else{Y=Kp;var ee=Hp}else(V=I.nodeName)&&V.toLowerCase()==="input"&&(I.type==="checkbox"||I.type==="radio")&&(Y=Wp);if(Y&&(Y=Y(e,O))){ra(D,Y,n,L);break e}ee&&ee(e,I,O),e==="focusout"&&(ee=I._wrapperState)&&ee.controlled&&I.type==="number"&&Vo(I,"number",I.value)}switch(ee=O?bn(O):window,e){case"focusin":(na(ee)||ee.contentEditable==="true")&&(Qn=ee,Ss=O,Or=null);break;case"focusout":Or=Ss=Qn=null;break;case"mousedown":xs=!0;break;case"contextmenu":case"mouseup":case"dragend":xs=!1,da(D,n,L);break;case"selectionchange":if(Yp)break;case"keydown":case"keyup":da(D,n,L)}var te;if(vs)e:{switch(e){case"compositionstart":var ne="onCompositionStart";break e;case"compositionend":ne="onCompositionEnd";break e;case"compositionupdate":ne="onCompositionUpdate";break e}ne=void 0}else zn?ea(e,n)&&(ne="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ne="onCompositionStart");ne&&(Xu&&n.locale!=="ko"&&(zn||ne!=="onCompositionStart"?ne==="onCompositionEnd"&&zn&&(te=Hu()):(Wt=L,cs="value"in Wt?Wt.value:Wt.textContent,zn=!0)),ee=ki(O,ne),0<ee.length&&(ne=new Gu(ne,e,null,n,L),D.push({event:ne,listeners:ee}),te?ne.data=te:(te=ta(n),te!==null&&(ne.data=te)))),(te=Qp?$p(e,n):Bp(e,n))&&(O=ki(O,"onBeforeInput"),0<O.length&&(L=new Gu("onBeforeInput","beforeinput",null,n,L),D.push({event:L,listeners:O}),L.data=te))}xa(D,t)})}function Tr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ki(e,t){for(var n=t+"Capture",r=[];e!==null;){var u=e,c=u.stateNode;u.tag===5&&c!==null&&(u=c,c=fr(e,n),c!=null&&r.unshift(Tr(e,c,u)),c=fr(e,t),c!=null&&r.push(Tr(e,c,u))),e=e.return}return r}function Bn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ea(e,t,n,r,u){for(var c=t._reactName,d=[];n!==null&&n!==r;){var m=n,g=m.alternate,O=m.stateNode;if(g!==null&&g===r)break;m.tag===5&&O!==null&&(m=O,u?(g=fr(n,c),g!=null&&d.unshift(Tr(n,g,m))):u||(g=fr(n,c),g!=null&&d.push(Tr(n,g,m)))),n=n.return}d.length!==0&&e.push({event:t,listeners:d})}var nh=/\r\n?/g,rh=/\u0000|\uFFFD/g;function ka(e){return(typeof e=="string"?e:""+e).replace(nh,`
`).replace(rh,"")}function Ri(e,t,n){if(t=ka(t),ka(e)!==t&&n)throw Error(s(425))}function _i(){}var Os=null,Ps=null;function Ns(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ts=typeof setTimeout=="function"?setTimeout:void 0,ih=typeof clearTimeout=="function"?clearTimeout:void 0,Ra=typeof Promise=="function"?Promise:void 0,oh=typeof queueMicrotask=="function"?queueMicrotask:typeof Ra<"u"?function(e){return Ra.resolve(null).then(e).catch(sh)}:Ts;function sh(e){setTimeout(function(){throw e})}function Fs(e,t){var n=t,r=0;do{var u=n.nextSibling;if(e.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(r===0){e.removeChild(u),Sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=u}while(n);Sr(t)}function Jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function _a(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),Nt="__reactFiber$"+qn,Fr="__reactProps$"+qn,Lt="__reactContainer$"+qn,As="__reactEvents$"+qn,lh="__reactListeners$"+qn,uh="__reactHandles$"+qn;function vn(e){var t=e[Nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Lt]||n[Nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=_a(e);e!==null;){if(n=e[Nt])return n;e=_a(e)}return t}e=n,n=e.parentNode}return null}function Ar(e){return e=e[Nt]||e[Lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function bn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(s(33))}function Oi(e){return e[Fr]||null}var js=[],Vn=-1;function Xt(e){return{current:e}}function Ce(e){0>Vn||(e.current=js[Vn],js[Vn]=null,Vn--)}function we(e,t){Vn++,js[Vn]=e.current,e.current=t}var Yt={},He=Xt(Yt),et=Xt(!1),yn=Yt;function Hn(e,t){var n=e.type.contextTypes;if(!n)return Yt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var u={},c;for(c in n)u[c]=t[c];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=u),u}function tt(e){return e=e.childContextTypes,e!=null}function Pi(){Ce(et),Ce(He)}function Oa(e,t,n){if(He.current!==Yt)throw Error(s(168));we(He,t),we(et,n)}function Pa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var u in r)if(!(u in t))throw Error(s(108,ge(e)||"Unknown",u));return B({},n,r)}function Ni(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Yt,yn=He.current,we(He,e),we(et,et.current),!0}function Na(e,t,n){var r=e.stateNode;if(!r)throw Error(s(169));n?(e=Pa(e,t,yn),r.__reactInternalMemoizedMergedChildContext=e,Ce(et),Ce(He),we(He,e)):Ce(et),we(et,n)}var Dt=null,Ti=!1,Is=!1;function Ta(e){Dt===null?Dt=[e]:Dt.push(e)}function ah(e){Ti=!0,Ta(e)}function Zt(){if(!Is&&Dt!==null){Is=!0;var e=0,t=ve;try{var n=Dt;for(ve=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Dt=null,Ti=!1}catch(u){throw Dt!==null&&(Dt=Dt.slice(e+1)),Au(ns,Zt),u}finally{ve=t,Is=!1}}return null}var Kn=[],Wn=0,Fi=null,Ai=0,pt=[],ht=0,gn=null,Mt=1,Ut="";function wn(e,t){Kn[Wn++]=Ai,Kn[Wn++]=Fi,Fi=e,Ai=t}function Fa(e,t,n){pt[ht++]=Mt,pt[ht++]=Ut,pt[ht++]=gn,gn=e;var r=Mt;e=Ut;var u=32-wt(r)-1;r&=~(1<<u),n+=1;var c=32-wt(t)+u;if(30<c){var d=u-u%5;c=(r&(1<<d)-1).toString(32),r>>=d,u-=d,Mt=1<<32-wt(t)+u|n<<u|r,Ut=c+e}else Mt=1<<c|n<<u|r,Ut=e}function Ls(e){e.return!==null&&(wn(e,1),Fa(e,1,0))}function Ds(e){for(;e===Fi;)Fi=Kn[--Wn],Kn[Wn]=null,Ai=Kn[--Wn],Kn[Wn]=null;for(;e===gn;)gn=pt[--ht],pt[ht]=null,Ut=pt[--ht],pt[ht]=null,Mt=pt[--ht],pt[ht]=null}var ct=null,ft=null,ke=!1,xt=null;function Aa(e,t){var n=gt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ja(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ct=e,ft=Jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ct=e,ft=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=gn!==null?{id:Mt,overflow:Ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=gt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ct=e,ft=null,!0):!1;default:return!1}}function Ms(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Us(e){if(ke){var t=ft;if(t){var n=t;if(!ja(e,t)){if(Ms(e))throw Error(s(418));t=Jt(n.nextSibling);var r=ct;t&&ja(e,t)?Aa(r,n):(e.flags=e.flags&-4097|2,ke=!1,ct=e)}}else{if(Ms(e))throw Error(s(418));e.flags=e.flags&-4097|2,ke=!1,ct=e}}}function Ia(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ct=e}function ji(e){if(e!==ct)return!1;if(!ke)return Ia(e),ke=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ns(e.type,e.memoizedProps)),t&&(t=ft)){if(Ms(e))throw La(),Error(s(418));for(;t;)Aa(e,t),t=Jt(t.nextSibling)}if(Ia(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ft=Jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ft=null}}else ft=ct?Jt(e.stateNode.nextSibling):null;return!0}function La(){for(var e=ft;e;)e=Jt(e.nextSibling)}function Gn(){ft=ct=null,ke=!1}function zs(e){xt===null?xt=[e]:xt.push(e)}var ch=b.ReactCurrentBatchConfig;function jr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(s(309));var r=n.stateNode}if(!r)throw Error(s(147,e));var u=r,c=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===c?t.ref:(t=function(d){var m=u.refs;d===null?delete m[c]:m[c]=d},t._stringRef=c,t)}if(typeof e!="string")throw Error(s(284));if(!n._owner)throw Error(s(290,e))}return e}function Ii(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Da(e){var t=e._init;return t(e._payload)}function Ma(e){function t(C,S){if(e){var R=C.deletions;R===null?(C.deletions=[S],C.flags|=16):R.push(S)}}function n(C,S){if(!e)return null;for(;S!==null;)t(C,S),S=S.sibling;return null}function r(C,S){for(C=new Map;S!==null;)S.key!==null?C.set(S.key,S):C.set(S.index,S),S=S.sibling;return C}function u(C,S){return C=un(C,S),C.index=0,C.sibling=null,C}function c(C,S,R){return C.index=R,e?(R=C.alternate,R!==null?(R=R.index,R<S?(C.flags|=2,S):R):(C.flags|=2,S)):(C.flags|=1048576,S)}function d(C){return e&&C.alternate===null&&(C.flags|=2),C}function m(C,S,R,M){return S===null||S.tag!==6?(S=Tl(R,C.mode,M),S.return=C,S):(S=u(S,R),S.return=C,S)}function g(C,S,R,M){var Y=R.type;return Y===ue?L(C,S,R.props.children,M,R.key):S!==null&&(S.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===ae&&Da(Y)===S.type)?(M=u(S,R.props),M.ref=jr(C,S,R),M.return=C,M):(M=io(R.type,R.key,R.props,null,C.mode,M),M.ref=jr(C,S,R),M.return=C,M)}function O(C,S,R,M){return S===null||S.tag!==4||S.stateNode.containerInfo!==R.containerInfo||S.stateNode.implementation!==R.implementation?(S=Fl(R,C.mode,M),S.return=C,S):(S=u(S,R.children||[]),S.return=C,S)}function L(C,S,R,M,Y){return S===null||S.tag!==7?(S=On(R,C.mode,M,Y),S.return=C,S):(S=u(S,R),S.return=C,S)}function D(C,S,R){if(typeof S=="string"&&S!==""||typeof S=="number")return S=Tl(""+S,C.mode,R),S.return=C,S;if(typeof S=="object"&&S!==null){switch(S.$$typeof){case ie:return R=io(S.type,S.key,S.props,null,C.mode,R),R.ref=jr(C,null,S),R.return=C,R;case Z:return S=Fl(S,C.mode,R),S.return=C,S;case ae:var M=S._init;return D(C,M(S._payload),R)}if(ur(S)||X(S))return S=On(S,C.mode,R,null),S.return=C,S;Ii(C,S)}return null}function I(C,S,R,M){var Y=S!==null?S.key:null;if(typeof R=="string"&&R!==""||typeof R=="number")return Y!==null?null:m(C,S,""+R,M);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case ie:return R.key===Y?g(C,S,R,M):null;case Z:return R.key===Y?O(C,S,R,M):null;case ae:return Y=R._init,I(C,S,Y(R._payload),M)}if(ur(R)||X(R))return Y!==null?null:L(C,S,R,M,null);Ii(C,R)}return null}function V(C,S,R,M,Y){if(typeof M=="string"&&M!==""||typeof M=="number")return C=C.get(R)||null,m(S,C,""+M,Y);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case ie:return C=C.get(M.key===null?R:M.key)||null,g(S,C,M,Y);case Z:return C=C.get(M.key===null?R:M.key)||null,O(S,C,M,Y);case ae:var ee=M._init;return V(C,S,R,ee(M._payload),Y)}if(ur(M)||X(M))return C=C.get(R)||null,L(S,C,M,Y,null);Ii(S,M)}return null}function G(C,S,R,M){for(var Y=null,ee=null,te=S,ne=S=0,qe=null;te!==null&&ne<R.length;ne++){te.index>ne?(qe=te,te=null):qe=te.sibling;var pe=I(C,te,R[ne],M);if(pe===null){te===null&&(te=qe);break}e&&te&&pe.alternate===null&&t(C,te),S=c(pe,S,ne),ee===null?Y=pe:ee.sibling=pe,ee=pe,te=qe}if(ne===R.length)return n(C,te),ke&&wn(C,ne),Y;if(te===null){for(;ne<R.length;ne++)te=D(C,R[ne],M),te!==null&&(S=c(te,S,ne),ee===null?Y=te:ee.sibling=te,ee=te);return ke&&wn(C,ne),Y}for(te=r(C,te);ne<R.length;ne++)qe=V(te,C,ne,R[ne],M),qe!==null&&(e&&qe.alternate!==null&&te.delete(qe.key===null?ne:qe.key),S=c(qe,S,ne),ee===null?Y=qe:ee.sibling=qe,ee=qe);return e&&te.forEach(function(an){return t(C,an)}),ke&&wn(C,ne),Y}function J(C,S,R,M){var Y=X(R);if(typeof Y!="function")throw Error(s(150));if(R=Y.call(R),R==null)throw Error(s(151));for(var ee=Y=null,te=S,ne=S=0,qe=null,pe=R.next();te!==null&&!pe.done;ne++,pe=R.next()){te.index>ne?(qe=te,te=null):qe=te.sibling;var an=I(C,te,pe.value,M);if(an===null){te===null&&(te=qe);break}e&&te&&an.alternate===null&&t(C,te),S=c(an,S,ne),ee===null?Y=an:ee.sibling=an,ee=an,te=qe}if(pe.done)return n(C,te),ke&&wn(C,ne),Y;if(te===null){for(;!pe.done;ne++,pe=R.next())pe=D(C,pe.value,M),pe!==null&&(S=c(pe,S,ne),ee===null?Y=pe:ee.sibling=pe,ee=pe);return ke&&wn(C,ne),Y}for(te=r(C,te);!pe.done;ne++,pe=R.next())pe=V(te,C,ne,pe.value,M),pe!==null&&(e&&pe.alternate!==null&&te.delete(pe.key===null?ne:pe.key),S=c(pe,S,ne),ee===null?Y=pe:ee.sibling=pe,ee=pe);return e&&te.forEach(function(qh){return t(C,qh)}),ke&&wn(C,ne),Y}function Le(C,S,R,M){if(typeof R=="object"&&R!==null&&R.type===ue&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case ie:e:{for(var Y=R.key,ee=S;ee!==null;){if(ee.key===Y){if(Y=R.type,Y===ue){if(ee.tag===7){n(C,ee.sibling),S=u(ee,R.props.children),S.return=C,C=S;break e}}else if(ee.elementType===Y||typeof Y=="object"&&Y!==null&&Y.$$typeof===ae&&Da(Y)===ee.type){n(C,ee.sibling),S=u(ee,R.props),S.ref=jr(C,ee,R),S.return=C,C=S;break e}n(C,ee);break}else t(C,ee);ee=ee.sibling}R.type===ue?(S=On(R.props.children,C.mode,M,R.key),S.return=C,C=S):(M=io(R.type,R.key,R.props,null,C.mode,M),M.ref=jr(C,S,R),M.return=C,C=M)}return d(C);case Z:e:{for(ee=R.key;S!==null;){if(S.key===ee)if(S.tag===4&&S.stateNode.containerInfo===R.containerInfo&&S.stateNode.implementation===R.implementation){n(C,S.sibling),S=u(S,R.children||[]),S.return=C,C=S;break e}else{n(C,S);break}else t(C,S);S=S.sibling}S=Fl(R,C.mode,M),S.return=C,C=S}return d(C);case ae:return ee=R._init,Le(C,S,ee(R._payload),M)}if(ur(R))return G(C,S,R,M);if(X(R))return J(C,S,R,M);Ii(C,R)}return typeof R=="string"&&R!==""||typeof R=="number"?(R=""+R,S!==null&&S.tag===6?(n(C,S.sibling),S=u(S,R),S.return=C,C=S):(n(C,S),S=Tl(R,C.mode,M),S.return=C,C=S),d(C)):n(C,S)}return Le}var Jn=Ma(!0),Ua=Ma(!1),Li=Xt(null),Di=null,Xn=null,Qs=null;function $s(){Qs=Xn=Di=null}function Bs(e){var t=Li.current;Ce(Li),e._currentValue=t}function qs(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Yn(e,t){Di=e,Qs=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(nt=!0),e.firstContext=null)}function mt(e){var t=e._currentValue;if(Qs!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(Di===null)throw Error(s(308));Xn=e,Di.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var Sn=null;function bs(e){Sn===null?Sn=[e]:Sn.push(e)}function za(e,t,n,r){var u=t.interleaved;return u===null?(n.next=n,bs(t)):(n.next=u.next,u.next=n),t.interleaved=n,zt(e,r)}function zt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var en=!1;function Vs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Qa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Qt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function tn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(fe&2)!==0){var u=r.pending;return u===null?t.next=t:(t.next=u.next,u.next=t),r.pending=t,zt(e,n)}return u=r.interleaved,u===null?(t.next=t,bs(r)):(t.next=u.next,u.next=t),r.interleaved=t,zt(e,n)}function Mi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,os(e,n)}}function $a(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var u=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var d={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};c===null?u=c=d:c=c.next=d,n=n.next}while(n!==null);c===null?u=c=t:c=c.next=t}else u=c=t;n={baseState:r.baseState,firstBaseUpdate:u,lastBaseUpdate:c,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ui(e,t,n,r){var u=e.updateQueue;en=!1;var c=u.firstBaseUpdate,d=u.lastBaseUpdate,m=u.shared.pending;if(m!==null){u.shared.pending=null;var g=m,O=g.next;g.next=null,d===null?c=O:d.next=O,d=g;var L=e.alternate;L!==null&&(L=L.updateQueue,m=L.lastBaseUpdate,m!==d&&(m===null?L.firstBaseUpdate=O:m.next=O,L.lastBaseUpdate=g))}if(c!==null){var D=u.baseState;d=0,L=O=g=null,m=c;do{var I=m.lane,V=m.eventTime;if((r&I)===I){L!==null&&(L=L.next={eventTime:V,lane:0,tag:m.tag,payload:m.payload,callback:m.callback,next:null});e:{var G=e,J=m;switch(I=t,V=n,J.tag){case 1:if(G=J.payload,typeof G=="function"){D=G.call(V,D,I);break e}D=G;break e;case 3:G.flags=G.flags&-65537|128;case 0:if(G=J.payload,I=typeof G=="function"?G.call(V,D,I):G,I==null)break e;D=B({},D,I);break e;case 2:en=!0}}m.callback!==null&&m.lane!==0&&(e.flags|=64,I=u.effects,I===null?u.effects=[m]:I.push(m))}else V={eventTime:V,lane:I,tag:m.tag,payload:m.payload,callback:m.callback,next:null},L===null?(O=L=V,g=D):L=L.next=V,d|=I;if(m=m.next,m===null){if(m=u.shared.pending,m===null)break;I=m,m=I.next,I.next=null,u.lastBaseUpdate=I,u.shared.pending=null}}while(!0);if(L===null&&(g=D),u.baseState=g,u.firstBaseUpdate=O,u.lastBaseUpdate=L,t=u.shared.interleaved,t!==null){u=t;do d|=u.lane,u=u.next;while(u!==t)}else c===null&&(u.shared.lanes=0);En|=d,e.lanes=d,e.memoizedState=D}}function Ba(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],u=r.callback;if(u!==null){if(r.callback=null,r=n,typeof u!="function")throw Error(s(191,u));u.call(r)}}}var Ir={},Tt=Xt(Ir),Lr=Xt(Ir),Dr=Xt(Ir);function xn(e){if(e===Ir)throw Error(s(174));return e}function Hs(e,t){switch(we(Dr,t),we(Lr,e),we(Tt,Ir),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ko(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ko(t,e)}Ce(Tt),we(Tt,t)}function Zn(){Ce(Tt),Ce(Lr),Ce(Dr)}function qa(e){xn(Dr.current);var t=xn(Tt.current),n=Ko(t,e.type);t!==n&&(we(Lr,e),we(Tt,n))}function Ks(e){Lr.current===e&&(Ce(Tt),Ce(Lr))}var Oe=Xt(0);function zi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ws=[];function Gs(){for(var e=0;e<Ws.length;e++)Ws[e]._workInProgressVersionPrimary=null;Ws.length=0}var Qi=b.ReactCurrentDispatcher,Js=b.ReactCurrentBatchConfig,Cn=0,Pe=null,Ue=null,$e=null,$i=!1,Mr=!1,Ur=0,fh=0;function Ke(){throw Error(s(321))}function Xs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!St(e[n],t[n]))return!1;return!0}function Ys(e,t,n,r,u,c){if(Cn=c,Pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Qi.current=e===null||e.memoizedState===null?mh:vh,e=n(r,u),Mr){c=0;do{if(Mr=!1,Ur=0,25<=c)throw Error(s(301));c+=1,$e=Ue=null,t.updateQueue=null,Qi.current=yh,e=n(r,u)}while(Mr)}if(Qi.current=bi,t=Ue!==null&&Ue.next!==null,Cn=0,$e=Ue=Pe=null,$i=!1,t)throw Error(s(300));return e}function Zs(){var e=Ur!==0;return Ur=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?Pe.memoizedState=$e=e:$e=$e.next=e,$e}function vt(){if(Ue===null){var e=Pe.alternate;e=e!==null?e.memoizedState:null}else e=Ue.next;var t=$e===null?Pe.memoizedState:$e.next;if(t!==null)$e=t,Ue=e;else{if(e===null)throw Error(s(310));Ue=e,e={memoizedState:Ue.memoizedState,baseState:Ue.baseState,baseQueue:Ue.baseQueue,queue:Ue.queue,next:null},$e===null?Pe.memoizedState=$e=e:$e=$e.next=e}return $e}function zr(e,t){return typeof t=="function"?t(e):t}function el(e){var t=vt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var r=Ue,u=r.baseQueue,c=n.pending;if(c!==null){if(u!==null){var d=u.next;u.next=c.next,c.next=d}r.baseQueue=u=c,n.pending=null}if(u!==null){c=u.next,r=r.baseState;var m=d=null,g=null,O=c;do{var L=O.lane;if((Cn&L)===L)g!==null&&(g=g.next={lane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),r=O.hasEagerState?O.eagerState:e(r,O.action);else{var D={lane:L,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null};g===null?(m=g=D,d=r):g=g.next=D,Pe.lanes|=L,En|=L}O=O.next}while(O!==null&&O!==c);g===null?d=r:g.next=m,St(r,t.memoizedState)||(nt=!0),t.memoizedState=r,t.baseState=d,t.baseQueue=g,n.lastRenderedState=r}if(e=n.interleaved,e!==null){u=e;do c=u.lane,Pe.lanes|=c,En|=c,u=u.next;while(u!==e)}else u===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function tl(e){var t=vt(),n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=e;var r=n.dispatch,u=n.pending,c=t.memoizedState;if(u!==null){n.pending=null;var d=u=u.next;do c=e(c,d.action),d=d.next;while(d!==u);St(c,t.memoizedState)||(nt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),n.lastRenderedState=c}return[c,r]}function ba(){}function Va(e,t){var n=Pe,r=vt(),u=t(),c=!St(r.memoizedState,u);if(c&&(r.memoizedState=u,nt=!0),r=r.queue,nl(Wa.bind(null,n,r,e),[e]),r.getSnapshot!==t||c||$e!==null&&$e.memoizedState.tag&1){if(n.flags|=2048,Qr(9,Ka.bind(null,n,r,u,t),void 0,null),Be===null)throw Error(s(349));(Cn&30)!==0||Ha(n,t,u)}return u}function Ha(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Pe.updateQueue,t===null?(t={lastEffect:null,stores:null},Pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ka(e,t,n,r){t.value=n,t.getSnapshot=r,Ga(t)&&Ja(e)}function Wa(e,t,n){return n(function(){Ga(t)&&Ja(e)})}function Ga(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!St(e,n)}catch{return!0}}function Ja(e){var t=zt(e,1);t!==null&&Rt(t,e,1,-1)}function Xa(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:zr,lastRenderedState:e},t.queue=e,e=e.dispatch=hh.bind(null,Pe,e),[t.memoizedState,e]}function Qr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Pe.updateQueue,t===null?(t={lastEffect:null,stores:null},Pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Ya(){return vt().memoizedState}function Bi(e,t,n,r){var u=Ft();Pe.flags|=e,u.memoizedState=Qr(1|t,n,void 0,r===void 0?null:r)}function qi(e,t,n,r){var u=vt();r=r===void 0?null:r;var c=void 0;if(Ue!==null){var d=Ue.memoizedState;if(c=d.destroy,r!==null&&Xs(r,d.deps)){u.memoizedState=Qr(t,n,c,r);return}}Pe.flags|=e,u.memoizedState=Qr(1|t,n,c,r)}function Za(e,t){return Bi(8390656,8,e,t)}function nl(e,t){return qi(2048,8,e,t)}function ec(e,t){return qi(4,2,e,t)}function tc(e,t){return qi(4,4,e,t)}function nc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function rc(e,t,n){return n=n!=null?n.concat([e]):null,qi(4,4,nc.bind(null,t,e),n)}function rl(){}function ic(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Xs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function oc(e,t){var n=vt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Xs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function sc(e,t,n){return(Cn&21)===0?(e.baseState&&(e.baseState=!1,nt=!0),e.memoizedState=n):(St(n,t)||(n=Du(),Pe.lanes|=n,En|=n,e.baseState=!0),t)}function dh(e,t){var n=ve;ve=n!==0&&4>n?n:4,e(!0);var r=Js.transition;Js.transition={};try{e(!1),t()}finally{ve=n,Js.transition=r}}function lc(){return vt().memoizedState}function ph(e,t,n){var r=sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},uc(e))ac(t,n);else if(n=za(e,t,n,r),n!==null){var u=Ze();Rt(n,e,r,u),cc(n,t,r)}}function hh(e,t,n){var r=sn(e),u={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(uc(e))ac(t,u);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var d=t.lastRenderedState,m=c(d,n);if(u.hasEagerState=!0,u.eagerState=m,St(m,d)){var g=t.interleaved;g===null?(u.next=u,bs(t)):(u.next=g.next,g.next=u),t.interleaved=u;return}}catch{}finally{}n=za(e,t,u,r),n!==null&&(u=Ze(),Rt(n,e,r,u),cc(n,t,r))}}function uc(e){var t=e.alternate;return e===Pe||t!==null&&t===Pe}function ac(e,t){Mr=$i=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cc(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,os(e,n)}}var bi={readContext:mt,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useInsertionEffect:Ke,useLayoutEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useMutableSource:Ke,useSyncExternalStore:Ke,useId:Ke,unstable_isNewReconciler:!1},mh={readContext:mt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:mt,useEffect:Za,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Bi(4194308,4,nc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Bi(4,2,e,t)},useMemo:function(e,t){var n=Ft();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ft();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ph.bind(null,Pe,e),[r.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:Xa,useDebugValue:rl,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=Xa(!1),t=e[0];return e=dh.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Pe,u=Ft();if(ke){if(n===void 0)throw Error(s(407));n=n()}else{if(n=t(),Be===null)throw Error(s(349));(Cn&30)!==0||Ha(r,t,n)}u.memoizedState=n;var c={value:n,getSnapshot:t};return u.queue=c,Za(Wa.bind(null,r,c,e),[e]),r.flags|=2048,Qr(9,Ka.bind(null,r,c,n,t),void 0,null),n},useId:function(){var e=Ft(),t=Be.identifierPrefix;if(ke){var n=Ut,r=Mt;n=(r&~(1<<32-wt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=fh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},vh={readContext:mt,useCallback:ic,useContext:mt,useEffect:nl,useImperativeHandle:rc,useInsertionEffect:ec,useLayoutEffect:tc,useMemo:oc,useReducer:el,useRef:Ya,useState:function(){return el(zr)},useDebugValue:rl,useDeferredValue:function(e){var t=vt();return sc(t,Ue.memoizedState,e)},useTransition:function(){var e=el(zr)[0],t=vt().memoizedState;return[e,t]},useMutableSource:ba,useSyncExternalStore:Va,useId:lc,unstable_isNewReconciler:!1},yh={readContext:mt,useCallback:ic,useContext:mt,useEffect:nl,useImperativeHandle:rc,useInsertionEffect:ec,useLayoutEffect:tc,useMemo:oc,useReducer:tl,useRef:Ya,useState:function(){return tl(zr)},useDebugValue:rl,useDeferredValue:function(e){var t=vt();return Ue===null?t.memoizedState=e:sc(t,Ue.memoizedState,e)},useTransition:function(){var e=tl(zr)[0],t=vt().memoizedState;return[e,t]},useMutableSource:ba,useSyncExternalStore:Va,useId:lc,unstable_isNewReconciler:!1};function Ct(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:B({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Vi={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ze(),u=sn(e),c=Qt(r,u);c.payload=t,n!=null&&(c.callback=n),t=tn(e,c,u),t!==null&&(Rt(t,e,u,r),Mi(t,e,u))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ze(),u=sn(e),c=Qt(r,u);c.tag=1,c.payload=t,n!=null&&(c.callback=n),t=tn(e,c,u),t!==null&&(Rt(t,e,u,r),Mi(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ze(),r=sn(e),u=Qt(n,r);u.tag=2,t!=null&&(u.callback=t),t=tn(e,u,r),t!==null&&(Rt(t,e,r,n),Mi(t,e,r))}};function fc(e,t,n,r,u,c,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,c,d):t.prototype&&t.prototype.isPureReactComponent?!_r(n,r)||!_r(u,c):!0}function dc(e,t,n){var r=!1,u=Yt,c=t.contextType;return typeof c=="object"&&c!==null?c=mt(c):(u=tt(t)?yn:He.current,r=t.contextTypes,c=(r=r!=null)?Hn(e,u):Yt),t=new t(n,c),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Vi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=c),t}function pc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Vi.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var u=e.stateNode;u.props=n,u.state=e.memoizedState,u.refs={},Vs(e);var c=t.contextType;typeof c=="object"&&c!==null?u.context=mt(c):(c=tt(t)?yn:He.current,u.context=Hn(e,c)),u.state=e.memoizedState,c=t.getDerivedStateFromProps,typeof c=="function"&&(il(e,t,c,n),u.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(t=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),t!==u.state&&Vi.enqueueReplaceState(u,u.state,null),Ui(e,n,u,r),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function er(e,t){try{var n="",r=t;do n+=re(r),r=r.return;while(r);var u=n}catch(c){u=`
Error generating stack: `+c.message+`
`+c.stack}return{value:e,source:t,stack:u,digest:null}}function sl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ll(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var gh=typeof WeakMap=="function"?WeakMap:Map;function hc(e,t,n){n=Qt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Yi||(Yi=!0,Cl=r),ll(e,t)},n}function mc(e,t,n){n=Qt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var u=t.value;n.payload=function(){return r(u)},n.callback=function(){ll(e,t)}}var c=e.stateNode;return c!==null&&typeof c.componentDidCatch=="function"&&(n.callback=function(){ll(e,t),typeof r!="function"&&(rn===null?rn=new Set([this]):rn.add(this));var d=t.stack;this.componentDidCatch(t.value,{componentStack:d!==null?d:""})}),n}function vc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new gh;var u=new Set;r.set(t,u)}else u=r.get(t),u===void 0&&(u=new Set,r.set(t,u));u.has(n)||(u.add(n),e=Ah.bind(null,e,t,n),t.then(e,e))}function yc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function gc(e,t,n,r,u){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Qt(-1,1),t.tag=2,tn(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var wh=b.ReactCurrentOwner,nt=!1;function Ye(e,t,n,r){t.child=e===null?Ua(t,null,n,r):Jn(t,e.child,n,r)}function wc(e,t,n,r,u){n=n.render;var c=t.ref;return Yn(t,u),r=Ys(e,t,n,r,c,u),n=Zs(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,$t(e,t,u)):(ke&&n&&Ls(t),t.flags|=1,Ye(e,t,r,u),t.child)}function Sc(e,t,n,r,u){if(e===null){var c=n.type;return typeof c=="function"&&!Nl(c)&&c.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=c,xc(e,t,c,r,u)):(e=io(n.type,null,r,t,t.mode,u),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,(e.lanes&u)===0){var d=c.memoizedProps;if(n=n.compare,n=n!==null?n:_r,n(d,r)&&e.ref===t.ref)return $t(e,t,u)}return t.flags|=1,e=un(c,r),e.ref=t.ref,e.return=t,t.child=e}function xc(e,t,n,r,u){if(e!==null){var c=e.memoizedProps;if(_r(c,r)&&e.ref===t.ref)if(nt=!1,t.pendingProps=r=c,(e.lanes&u)!==0)(e.flags&131072)!==0&&(nt=!0);else return t.lanes=e.lanes,$t(e,t,u)}return ul(e,t,n,r,u)}function Cc(e,t,n){var r=t.pendingProps,u=r.children,c=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},we(nr,dt),dt|=n;else{if((n&1073741824)===0)return e=c!==null?c.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,we(nr,dt),dt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=c!==null?c.baseLanes:n,we(nr,dt),dt|=r}else c!==null?(r=c.baseLanes|n,t.memoizedState=null):r=n,we(nr,dt),dt|=r;return Ye(e,t,u,n),t.child}function Ec(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ul(e,t,n,r,u){var c=tt(n)?yn:He.current;return c=Hn(t,c),Yn(t,u),n=Ys(e,t,n,r,c,u),r=Zs(),e!==null&&!nt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~u,$t(e,t,u)):(ke&&r&&Ls(t),t.flags|=1,Ye(e,t,n,u),t.child)}function kc(e,t,n,r,u){if(tt(n)){var c=!0;Ni(t)}else c=!1;if(Yn(t,u),t.stateNode===null)Ki(e,t),dc(t,n,r),ol(t,n,r,u),r=!0;else if(e===null){var d=t.stateNode,m=t.memoizedProps;d.props=m;var g=d.context,O=n.contextType;typeof O=="object"&&O!==null?O=mt(O):(O=tt(n)?yn:He.current,O=Hn(t,O));var L=n.getDerivedStateFromProps,D=typeof L=="function"||typeof d.getSnapshotBeforeUpdate=="function";D||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(m!==r||g!==O)&&pc(t,d,r,O),en=!1;var I=t.memoizedState;d.state=I,Ui(t,r,d,u),g=t.memoizedState,m!==r||I!==g||et.current||en?(typeof L=="function"&&(il(t,n,L,r),g=t.memoizedState),(m=en||fc(t,n,m,r,I,g,O))?(D||typeof d.UNSAFE_componentWillMount!="function"&&typeof d.componentWillMount!="function"||(typeof d.componentWillMount=="function"&&d.componentWillMount(),typeof d.UNSAFE_componentWillMount=="function"&&d.UNSAFE_componentWillMount()),typeof d.componentDidMount=="function"&&(t.flags|=4194308)):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=g),d.props=r,d.state=g,d.context=O,r=m):(typeof d.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{d=t.stateNode,Qa(e,t),m=t.memoizedProps,O=t.type===t.elementType?m:Ct(t.type,m),d.props=O,D=t.pendingProps,I=d.context,g=n.contextType,typeof g=="object"&&g!==null?g=mt(g):(g=tt(n)?yn:He.current,g=Hn(t,g));var V=n.getDerivedStateFromProps;(L=typeof V=="function"||typeof d.getSnapshotBeforeUpdate=="function")||typeof d.UNSAFE_componentWillReceiveProps!="function"&&typeof d.componentWillReceiveProps!="function"||(m!==D||I!==g)&&pc(t,d,r,g),en=!1,I=t.memoizedState,d.state=I,Ui(t,r,d,u);var G=t.memoizedState;m!==D||I!==G||et.current||en?(typeof V=="function"&&(il(t,n,V,r),G=t.memoizedState),(O=en||fc(t,n,O,r,I,G,g)||!1)?(L||typeof d.UNSAFE_componentWillUpdate!="function"&&typeof d.componentWillUpdate!="function"||(typeof d.componentWillUpdate=="function"&&d.componentWillUpdate(r,G,g),typeof d.UNSAFE_componentWillUpdate=="function"&&d.UNSAFE_componentWillUpdate(r,G,g)),typeof d.componentDidUpdate=="function"&&(t.flags|=4),typeof d.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof d.componentDidUpdate!="function"||m===e.memoizedProps&&I===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&I===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=G),d.props=r,d.state=G,d.context=g,r=O):(typeof d.componentDidUpdate!="function"||m===e.memoizedProps&&I===e.memoizedState||(t.flags|=4),typeof d.getSnapshotBeforeUpdate!="function"||m===e.memoizedProps&&I===e.memoizedState||(t.flags|=1024),r=!1)}return al(e,t,n,r,c,u)}function al(e,t,n,r,u,c){Ec(e,t);var d=(t.flags&128)!==0;if(!r&&!d)return u&&Na(t,n,!1),$t(e,t,c);r=t.stateNode,wh.current=t;var m=d&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&d?(t.child=Jn(t,e.child,null,c),t.child=Jn(t,null,m,c)):Ye(e,t,m,c),t.memoizedState=r.state,u&&Na(t,n,!0),t.child}function Rc(e){var t=e.stateNode;t.pendingContext?Oa(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Oa(e,t.context,!1),Hs(e,t.containerInfo)}function _c(e,t,n,r,u){return Gn(),zs(u),t.flags|=256,Ye(e,t,n,r),t.child}var cl={dehydrated:null,treeContext:null,retryLane:0};function fl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Oc(e,t,n){var r=t.pendingProps,u=Oe.current,c=!1,d=(t.flags&128)!==0,m;if((m=d)||(m=e!==null&&e.memoizedState===null?!1:(u&2)!==0),m?(c=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),we(Oe,u&1),e===null)return Us(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(d=r.children,e=r.fallback,c?(r=t.mode,c=t.child,d={mode:"hidden",children:d},(r&1)===0&&c!==null?(c.childLanes=0,c.pendingProps=d):c=oo(d,r,0,null),e=On(e,r,n,null),c.return=t,e.return=t,c.sibling=e,t.child=c,t.child.memoizedState=fl(n),t.memoizedState=cl,e):dl(t,d));if(u=e.memoizedState,u!==null&&(m=u.dehydrated,m!==null))return Sh(e,t,d,r,m,u,n);if(c){c=r.fallback,d=t.mode,u=e.child,m=u.sibling;var g={mode:"hidden",children:r.children};return(d&1)===0&&t.child!==u?(r=t.child,r.childLanes=0,r.pendingProps=g,t.deletions=null):(r=un(u,g),r.subtreeFlags=u.subtreeFlags&14680064),m!==null?c=un(m,c):(c=On(c,d,n,null),c.flags|=2),c.return=t,r.return=t,r.sibling=c,t.child=r,r=c,c=t.child,d=e.child.memoizedState,d=d===null?fl(n):{baseLanes:d.baseLanes|n,cachePool:null,transitions:d.transitions},c.memoizedState=d,c.childLanes=e.childLanes&~n,t.memoizedState=cl,r}return c=e.child,e=c.sibling,r=un(c,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function dl(e,t){return t=oo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Hi(e,t,n,r){return r!==null&&zs(r),Jn(t,e.child,null,n),e=dl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Sh(e,t,n,r,u,c,d){if(n)return t.flags&256?(t.flags&=-257,r=sl(Error(s(422))),Hi(e,t,d,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(c=r.fallback,u=t.mode,r=oo({mode:"visible",children:r.children},u,0,null),c=On(c,u,d,null),c.flags|=2,r.return=t,c.return=t,r.sibling=c,t.child=r,(t.mode&1)!==0&&Jn(t,e.child,null,d),t.child.memoizedState=fl(d),t.memoizedState=cl,c);if((t.mode&1)===0)return Hi(e,t,d,null);if(u.data==="$!"){if(r=u.nextSibling&&u.nextSibling.dataset,r)var m=r.dgst;return r=m,c=Error(s(419)),r=sl(c,r,void 0),Hi(e,t,d,r)}if(m=(d&e.childLanes)!==0,nt||m){if(r=Be,r!==null){switch(d&-d){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(r.suspendedLanes|d))!==0?0:u,u!==0&&u!==c.retryLane&&(c.retryLane=u,zt(e,u),Rt(r,e,u,-1))}return Pl(),r=sl(Error(s(421))),Hi(e,t,d,r)}return u.data==="$?"?(t.flags|=128,t.child=e.child,t=jh.bind(null,e),u._reactRetry=t,null):(e=c.treeContext,ft=Jt(u.nextSibling),ct=t,ke=!0,xt=null,e!==null&&(pt[ht++]=Mt,pt[ht++]=Ut,pt[ht++]=gn,Mt=e.id,Ut=e.overflow,gn=t),t=dl(t,r.children),t.flags|=4096,t)}function Pc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),qs(e.return,t,n)}function pl(e,t,n,r,u){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:u}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=r,c.tail=n,c.tailMode=u)}function Nc(e,t,n){var r=t.pendingProps,u=r.revealOrder,c=r.tail;if(Ye(e,t,r.children,n),r=Oe.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Pc(e,n,t);else if(e.tag===19)Pc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(we(Oe,r),(t.mode&1)===0)t.memoizedState=null;else switch(u){case"forwards":for(n=t.child,u=null;n!==null;)e=n.alternate,e!==null&&zi(e)===null&&(u=n),n=n.sibling;n=u,n===null?(u=t.child,t.child=null):(u=n.sibling,n.sibling=null),pl(t,!1,u,n,c);break;case"backwards":for(n=null,u=t.child,t.child=null;u!==null;){if(e=u.alternate,e!==null&&zi(e)===null){t.child=u;break}e=u.sibling,u.sibling=n,n=u,u=e}pl(t,!0,n,null,c);break;case"together":pl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ki(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),En|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,n=un(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=un(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function xh(e,t,n){switch(t.tag){case 3:Rc(t),Gn();break;case 5:qa(t);break;case 1:tt(t.type)&&Ni(t);break;case 4:Hs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,u=t.memoizedProps.value;we(Li,r._currentValue),r._currentValue=u;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(we(Oe,Oe.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Oc(e,t,n):(we(Oe,Oe.current&1),e=$t(e,t,n),e!==null?e.sibling:null);we(Oe,Oe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return Nc(e,t,n);t.flags|=128}if(u=t.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),we(Oe,Oe.current),r)break;return null;case 22:case 23:return t.lanes=0,Cc(e,t,n)}return $t(e,t,n)}var Tc,hl,Fc,Ac;Tc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},hl=function(){},Fc=function(e,t,n,r){var u=e.memoizedProps;if(u!==r){e=t.stateNode,xn(Tt.current);var c=null;switch(n){case"input":u=qo(e,u),r=qo(e,r),c=[];break;case"select":u=B({},u,{value:void 0}),r=B({},r,{value:void 0}),c=[];break;case"textarea":u=Ho(e,u),r=Ho(e,r),c=[];break;default:typeof u.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=_i)}Wo(n,r);var d;n=null;for(O in u)if(!r.hasOwnProperty(O)&&u.hasOwnProperty(O)&&u[O]!=null)if(O==="style"){var m=u[O];for(d in m)m.hasOwnProperty(d)&&(n||(n={}),n[d]="")}else O!=="dangerouslySetInnerHTML"&&O!=="children"&&O!=="suppressContentEditableWarning"&&O!=="suppressHydrationWarning"&&O!=="autoFocus"&&(a.hasOwnProperty(O)?c||(c=[]):(c=c||[]).push(O,null));for(O in r){var g=r[O];if(m=u!=null?u[O]:void 0,r.hasOwnProperty(O)&&g!==m&&(g!=null||m!=null))if(O==="style")if(m){for(d in m)!m.hasOwnProperty(d)||g&&g.hasOwnProperty(d)||(n||(n={}),n[d]="");for(d in g)g.hasOwnProperty(d)&&m[d]!==g[d]&&(n||(n={}),n[d]=g[d])}else n||(c||(c=[]),c.push(O,n)),n=g;else O==="dangerouslySetInnerHTML"?(g=g?g.__html:void 0,m=m?m.__html:void 0,g!=null&&m!==g&&(c=c||[]).push(O,g)):O==="children"?typeof g!="string"&&typeof g!="number"||(c=c||[]).push(O,""+g):O!=="suppressContentEditableWarning"&&O!=="suppressHydrationWarning"&&(a.hasOwnProperty(O)?(g!=null&&O==="onScroll"&&xe("scroll",e),c||m===g||(c=[])):(c=c||[]).push(O,g))}n&&(c=c||[]).push("style",n);var O=c;(t.updateQueue=O)&&(t.flags|=4)}},Ac=function(e,t,n,r){n!==r&&(t.flags|=4)};function $r(e,t){if(!ke)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function We(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var u=e.child;u!==null;)n|=u.lanes|u.childLanes,r|=u.subtreeFlags&14680064,r|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)n|=u.lanes|u.childLanes,r|=u.subtreeFlags,r|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ch(e,t,n){var r=t.pendingProps;switch(Ds(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return We(t),null;case 1:return tt(t.type)&&Pi(),We(t),null;case 3:return r=t.stateNode,Zn(),Ce(et),Ce(He),Gs(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ji(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,xt!==null&&(Rl(xt),xt=null))),hl(e,t),We(t),null;case 5:Ks(t);var u=xn(Dr.current);if(n=t.type,e!==null&&t.stateNode!=null)Fc(e,t,n,r,u),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(s(166));return We(t),null}if(e=xn(Tt.current),ji(t)){r=t.stateNode,n=t.type;var c=t.memoizedProps;switch(r[Nt]=t,r[Fr]=c,e=(t.mode&1)!==0,n){case"dialog":xe("cancel",r),xe("close",r);break;case"iframe":case"object":case"embed":xe("load",r);break;case"video":case"audio":for(u=0;u<Pr.length;u++)xe(Pr[u],r);break;case"source":xe("error",r);break;case"img":case"image":case"link":xe("error",r),xe("load",r);break;case"details":xe("toggle",r);break;case"input":du(r,c),xe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!c.multiple},xe("invalid",r);break;case"textarea":mu(r,c),xe("invalid",r)}Wo(n,c),u=null;for(var d in c)if(c.hasOwnProperty(d)){var m=c[d];d==="children"?typeof m=="string"?r.textContent!==m&&(c.suppressHydrationWarning!==!0&&Ri(r.textContent,m,e),u=["children",m]):typeof m=="number"&&r.textContent!==""+m&&(c.suppressHydrationWarning!==!0&&Ri(r.textContent,m,e),u=["children",""+m]):a.hasOwnProperty(d)&&m!=null&&d==="onScroll"&&xe("scroll",r)}switch(n){case"input":ni(r),hu(r,c,!0);break;case"textarea":ni(r),yu(r);break;case"select":case"option":break;default:typeof c.onClick=="function"&&(r.onclick=_i)}r=u,t.updateQueue=r,r!==null&&(t.flags|=4)}else{d=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=d.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=d.createElement(n,{is:r.is}):(e=d.createElement(n),n==="select"&&(d=e,r.multiple?d.multiple=!0:r.size&&(d.size=r.size))):e=d.createElementNS(e,n),e[Nt]=t,e[Fr]=r,Tc(e,t,!1,!1),t.stateNode=e;e:{switch(d=Go(n,r),n){case"dialog":xe("cancel",e),xe("close",e),u=r;break;case"iframe":case"object":case"embed":xe("load",e),u=r;break;case"video":case"audio":for(u=0;u<Pr.length;u++)xe(Pr[u],e);u=r;break;case"source":xe("error",e),u=r;break;case"img":case"image":case"link":xe("error",e),xe("load",e),u=r;break;case"details":xe("toggle",e),u=r;break;case"input":du(e,r),u=qo(e,r),xe("invalid",e);break;case"option":u=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},u=B({},r,{value:void 0}),xe("invalid",e);break;case"textarea":mu(e,r),u=Ho(e,r),xe("invalid",e);break;default:u=r}Wo(n,u),m=u;for(c in m)if(m.hasOwnProperty(c)){var g=m[c];c==="style"?xu(e,g):c==="dangerouslySetInnerHTML"?(g=g?g.__html:void 0,g!=null&&wu(e,g)):c==="children"?typeof g=="string"?(n!=="textarea"||g!=="")&&ar(e,g):typeof g=="number"&&ar(e,""+g):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(a.hasOwnProperty(c)?g!=null&&c==="onScroll"&&xe("scroll",e):g!=null&&z(e,c,g,d))}switch(n){case"input":ni(e),hu(e,r,!1);break;case"textarea":ni(e),yu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+me(r.value));break;case"select":e.multiple=!!r.multiple,c=r.value,c!=null?In(e,!!r.multiple,c,!1):r.defaultValue!=null&&In(e,!!r.multiple,r.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=_i)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return We(t),null;case 6:if(e&&t.stateNode!=null)Ac(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(s(166));if(n=xn(Dr.current),xn(Tt.current),ji(t)){if(r=t.stateNode,n=t.memoizedProps,r[Nt]=t,(c=r.nodeValue!==n)&&(e=ct,e!==null))switch(e.tag){case 3:Ri(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ri(r.nodeValue,n,(e.mode&1)!==0)}c&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=t,t.stateNode=r}return We(t),null;case 13:if(Ce(Oe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ke&&ft!==null&&(t.mode&1)!==0&&(t.flags&128)===0)La(),Gn(),t.flags|=98560,c=!1;else if(c=ji(t),r!==null&&r.dehydrated!==null){if(e===null){if(!c)throw Error(s(318));if(c=t.memoizedState,c=c!==null?c.dehydrated:null,!c)throw Error(s(317));c[Nt]=t}else Gn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;We(t),c=!1}else xt!==null&&(Rl(xt),xt=null),c=!0;if(!c)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Oe.current&1)!==0?ze===0&&(ze=3):Pl())),t.updateQueue!==null&&(t.flags|=4),We(t),null);case 4:return Zn(),hl(e,t),e===null&&Nr(t.stateNode.containerInfo),We(t),null;case 10:return Bs(t.type._context),We(t),null;case 17:return tt(t.type)&&Pi(),We(t),null;case 19:if(Ce(Oe),c=t.memoizedState,c===null)return We(t),null;if(r=(t.flags&128)!==0,d=c.rendering,d===null)if(r)$r(c,!1);else{if(ze!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(d=zi(e),d!==null){for(t.flags|=128,$r(c,!1),r=d.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)c=n,e=r,c.flags&=14680066,d=c.alternate,d===null?(c.childLanes=0,c.lanes=e,c.child=null,c.subtreeFlags=0,c.memoizedProps=null,c.memoizedState=null,c.updateQueue=null,c.dependencies=null,c.stateNode=null):(c.childLanes=d.childLanes,c.lanes=d.lanes,c.child=d.child,c.subtreeFlags=0,c.deletions=null,c.memoizedProps=d.memoizedProps,c.memoizedState=d.memoizedState,c.updateQueue=d.updateQueue,c.type=d.type,e=d.dependencies,c.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return we(Oe,Oe.current&1|2),t.child}e=e.sibling}c.tail!==null&&Ie()>rr&&(t.flags|=128,r=!0,$r(c,!1),t.lanes=4194304)}else{if(!r)if(e=zi(d),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),$r(c,!0),c.tail===null&&c.tailMode==="hidden"&&!d.alternate&&!ke)return We(t),null}else 2*Ie()-c.renderingStartTime>rr&&n!==1073741824&&(t.flags|=128,r=!0,$r(c,!1),t.lanes=4194304);c.isBackwards?(d.sibling=t.child,t.child=d):(n=c.last,n!==null?n.sibling=d:t.child=d,c.last=d)}return c.tail!==null?(t=c.tail,c.rendering=t,c.tail=t.sibling,c.renderingStartTime=Ie(),t.sibling=null,n=Oe.current,we(Oe,r?n&1|2:n&1),t):(We(t),null);case 22:case 23:return Ol(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(dt&1073741824)!==0&&(We(t),t.subtreeFlags&6&&(t.flags|=8192)):We(t),null;case 24:return null;case 25:return null}throw Error(s(156,t.tag))}function Eh(e,t){switch(Ds(t),t.tag){case 1:return tt(t.type)&&Pi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zn(),Ce(et),Ce(He),Gs(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Ks(t),null;case 13:if(Ce(Oe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Gn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ce(Oe),null;case 4:return Zn(),null;case 10:return Bs(t.type._context),null;case 22:case 23:return Ol(),null;case 24:return null;default:return null}}var Wi=!1,Ge=!1,kh=typeof WeakSet=="function"?WeakSet:Set,H=null;function tr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ae(e,t,r)}else n.current=null}function ml(e,t,n){try{n()}catch(r){Ae(e,t,r)}}var jc=!1;function Rh(e,t){if(Os=hi,e=fa(),ws(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var u=r.anchorOffset,c=r.focusNode;r=r.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break e}var d=0,m=-1,g=-1,O=0,L=0,D=e,I=null;t:for(;;){for(var V;D!==n||u!==0&&D.nodeType!==3||(m=d+u),D!==c||r!==0&&D.nodeType!==3||(g=d+r),D.nodeType===3&&(d+=D.nodeValue.length),(V=D.firstChild)!==null;)I=D,D=V;for(;;){if(D===e)break t;if(I===n&&++O===u&&(m=d),I===c&&++L===r&&(g=d),(V=D.nextSibling)!==null)break;D=I,I=D.parentNode}D=V}n=m===-1||g===-1?null:{start:m,end:g}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ps={focusedElem:e,selectionRange:n},hi=!1,H=t;H!==null;)if(t=H,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,H=e;else for(;H!==null;){t=H;try{var G=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(G!==null){var J=G.memoizedProps,Le=G.memoizedState,C=t.stateNode,S=C.getSnapshotBeforeUpdate(t.elementType===t.type?J:Ct(t.type,J),Le);C.__reactInternalSnapshotBeforeUpdate=S}break;case 3:var R=t.stateNode.containerInfo;R.nodeType===1?R.textContent="":R.nodeType===9&&R.documentElement&&R.removeChild(R.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(s(163))}}catch(M){Ae(t,t.return,M)}if(e=t.sibling,e!==null){e.return=t.return,H=e;break}H=t.return}return G=jc,jc=!1,G}function Br(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var u=r=r.next;do{if((u.tag&e)===e){var c=u.destroy;u.destroy=void 0,c!==void 0&&ml(t,n,c)}u=u.next}while(u!==r)}}function Gi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function vl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ic(e){var t=e.alternate;t!==null&&(e.alternate=null,Ic(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[Fr],delete t[As],delete t[lh],delete t[uh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Lc(e){return e.tag===5||e.tag===3||e.tag===4}function Dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Lc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function yl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=_i));else if(r!==4&&(e=e.child,e!==null))for(yl(e,t,n),e=e.sibling;e!==null;)yl(e,t,n),e=e.sibling}function gl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(gl(e,t,n),e=e.sibling;e!==null;)gl(e,t,n),e=e.sibling}var be=null,Et=!1;function nn(e,t,n){for(n=n.child;n!==null;)Mc(e,t,n),n=n.sibling}function Mc(e,t,n){if(Pt&&typeof Pt.onCommitFiberUnmount=="function")try{Pt.onCommitFiberUnmount(ui,n)}catch{}switch(n.tag){case 5:Ge||tr(n,t);case 6:var r=be,u=Et;be=null,nn(e,t,n),be=r,Et=u,be!==null&&(Et?(e=be,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):be.removeChild(n.stateNode));break;case 18:be!==null&&(Et?(e=be,n=n.stateNode,e.nodeType===8?Fs(e.parentNode,n):e.nodeType===1&&Fs(e,n),Sr(e)):Fs(be,n.stateNode));break;case 4:r=be,u=Et,be=n.stateNode.containerInfo,Et=!0,nn(e,t,n),be=r,Et=u;break;case 0:case 11:case 14:case 15:if(!Ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){u=r=r.next;do{var c=u,d=c.destroy;c=c.tag,d!==void 0&&((c&2)!==0||(c&4)!==0)&&ml(n,t,d),u=u.next}while(u!==r)}nn(e,t,n);break;case 1:if(!Ge&&(tr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(m){Ae(n,t,m)}nn(e,t,n);break;case 21:nn(e,t,n);break;case 22:n.mode&1?(Ge=(r=Ge)||n.memoizedState!==null,nn(e,t,n),Ge=r):nn(e,t,n);break;default:nn(e,t,n)}}function Uc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new kh),t.forEach(function(r){var u=Ih.bind(null,e,r);n.has(r)||(n.add(r),r.then(u,u))})}}function kt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var u=n[r];try{var c=e,d=t,m=d;e:for(;m!==null;){switch(m.tag){case 5:be=m.stateNode,Et=!1;break e;case 3:be=m.stateNode.containerInfo,Et=!0;break e;case 4:be=m.stateNode.containerInfo,Et=!0;break e}m=m.return}if(be===null)throw Error(s(160));Mc(c,d,u),be=null,Et=!1;var g=u.alternate;g!==null&&(g.return=null),u.return=null}catch(O){Ae(u,t,O)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)zc(t,e),t=t.sibling}function zc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(kt(t,e),At(e),r&4){try{Br(3,e,e.return),Gi(3,e)}catch(J){Ae(e,e.return,J)}try{Br(5,e,e.return)}catch(J){Ae(e,e.return,J)}}break;case 1:kt(t,e),At(e),r&512&&n!==null&&tr(n,n.return);break;case 5:if(kt(t,e),At(e),r&512&&n!==null&&tr(n,n.return),e.flags&32){var u=e.stateNode;try{ar(u,"")}catch(J){Ae(e,e.return,J)}}if(r&4&&(u=e.stateNode,u!=null)){var c=e.memoizedProps,d=n!==null?n.memoizedProps:c,m=e.type,g=e.updateQueue;if(e.updateQueue=null,g!==null)try{m==="input"&&c.type==="radio"&&c.name!=null&&pu(u,c),Go(m,d);var O=Go(m,c);for(d=0;d<g.length;d+=2){var L=g[d],D=g[d+1];L==="style"?xu(u,D):L==="dangerouslySetInnerHTML"?wu(u,D):L==="children"?ar(u,D):z(u,L,D,O)}switch(m){case"input":bo(u,c);break;case"textarea":vu(u,c);break;case"select":var I=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!c.multiple;var V=c.value;V!=null?In(u,!!c.multiple,V,!1):I!==!!c.multiple&&(c.defaultValue!=null?In(u,!!c.multiple,c.defaultValue,!0):In(u,!!c.multiple,c.multiple?[]:"",!1))}u[Fr]=c}catch(J){Ae(e,e.return,J)}}break;case 6:if(kt(t,e),At(e),r&4){if(e.stateNode===null)throw Error(s(162));u=e.stateNode,c=e.memoizedProps;try{u.nodeValue=c}catch(J){Ae(e,e.return,J)}}break;case 3:if(kt(t,e),At(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Sr(t.containerInfo)}catch(J){Ae(e,e.return,J)}break;case 4:kt(t,e),At(e);break;case 13:kt(t,e),At(e),u=e.child,u.flags&8192&&(c=u.memoizedState!==null,u.stateNode.isHidden=c,!c||u.alternate!==null&&u.alternate.memoizedState!==null||(xl=Ie())),r&4&&Uc(e);break;case 22:if(L=n!==null&&n.memoizedState!==null,e.mode&1?(Ge=(O=Ge)||L,kt(t,e),Ge=O):kt(t,e),At(e),r&8192){if(O=e.memoizedState!==null,(e.stateNode.isHidden=O)&&!L&&(e.mode&1)!==0)for(H=e,L=e.child;L!==null;){for(D=H=L;H!==null;){switch(I=H,V=I.child,I.tag){case 0:case 11:case 14:case 15:Br(4,I,I.return);break;case 1:tr(I,I.return);var G=I.stateNode;if(typeof G.componentWillUnmount=="function"){r=I,n=I.return;try{t=r,G.props=t.memoizedProps,G.state=t.memoizedState,G.componentWillUnmount()}catch(J){Ae(r,n,J)}}break;case 5:tr(I,I.return);break;case 22:if(I.memoizedState!==null){Bc(D);continue}}V!==null?(V.return=I,H=V):Bc(D)}L=L.sibling}e:for(L=null,D=e;;){if(D.tag===5){if(L===null){L=D;try{u=D.stateNode,O?(c=u.style,typeof c.setProperty=="function"?c.setProperty("display","none","important"):c.display="none"):(m=D.stateNode,g=D.memoizedProps.style,d=g!=null&&g.hasOwnProperty("display")?g.display:null,m.style.display=Su("display",d))}catch(J){Ae(e,e.return,J)}}}else if(D.tag===6){if(L===null)try{D.stateNode.nodeValue=O?"":D.memoizedProps}catch(J){Ae(e,e.return,J)}}else if((D.tag!==22&&D.tag!==23||D.memoizedState===null||D===e)&&D.child!==null){D.child.return=D,D=D.child;continue}if(D===e)break e;for(;D.sibling===null;){if(D.return===null||D.return===e)break e;L===D&&(L=null),D=D.return}L===D&&(L=null),D.sibling.return=D.return,D=D.sibling}}break;case 19:kt(t,e),At(e),r&4&&Uc(e);break;case 21:break;default:kt(t,e),At(e)}}function At(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Lc(n)){var r=n;break e}n=n.return}throw Error(s(160))}switch(r.tag){case 5:var u=r.stateNode;r.flags&32&&(ar(u,""),r.flags&=-33);var c=Dc(e);gl(e,c,u);break;case 3:case 4:var d=r.stateNode.containerInfo,m=Dc(e);yl(e,m,d);break;default:throw Error(s(161))}}catch(g){Ae(e,e.return,g)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function _h(e,t,n){H=e,Qc(e)}function Qc(e,t,n){for(var r=(e.mode&1)!==0;H!==null;){var u=H,c=u.child;if(u.tag===22&&r){var d=u.memoizedState!==null||Wi;if(!d){var m=u.alternate,g=m!==null&&m.memoizedState!==null||Ge;m=Wi;var O=Ge;if(Wi=d,(Ge=g)&&!O)for(H=u;H!==null;)d=H,g=d.child,d.tag===22&&d.memoizedState!==null?qc(u):g!==null?(g.return=d,H=g):qc(u);for(;c!==null;)H=c,Qc(c),c=c.sibling;H=u,Wi=m,Ge=O}$c(e)}else(u.subtreeFlags&8772)!==0&&c!==null?(c.return=u,H=c):$c(e)}}function $c(e){for(;H!==null;){var t=H;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Ge||Gi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ge)if(n===null)r.componentDidMount();else{var u=t.elementType===t.type?n.memoizedProps:Ct(t.type,n.memoizedProps);r.componentDidUpdate(u,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var c=t.updateQueue;c!==null&&Ba(t,c,r);break;case 3:var d=t.updateQueue;if(d!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ba(t,d,n)}break;case 5:var m=t.stateNode;if(n===null&&t.flags&4){n=m;var g=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":g.autoFocus&&n.focus();break;case"img":g.src&&(n.src=g.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var O=t.alternate;if(O!==null){var L=O.memoizedState;if(L!==null){var D=L.dehydrated;D!==null&&Sr(D)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(s(163))}Ge||t.flags&512&&vl(t)}catch(I){Ae(t,t.return,I)}}if(t===e){H=null;break}if(n=t.sibling,n!==null){n.return=t.return,H=n;break}H=t.return}}function Bc(e){for(;H!==null;){var t=H;if(t===e){H=null;break}var n=t.sibling;if(n!==null){n.return=t.return,H=n;break}H=t.return}}function qc(e){for(;H!==null;){var t=H;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Gi(4,t)}catch(g){Ae(t,n,g)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var u=t.return;try{r.componentDidMount()}catch(g){Ae(t,u,g)}}var c=t.return;try{vl(t)}catch(g){Ae(t,c,g)}break;case 5:var d=t.return;try{vl(t)}catch(g){Ae(t,d,g)}}}catch(g){Ae(t,t.return,g)}if(t===e){H=null;break}var m=t.sibling;if(m!==null){m.return=t.return,H=m;break}H=t.return}}var Oh=Math.ceil,Ji=b.ReactCurrentDispatcher,wl=b.ReactCurrentOwner,yt=b.ReactCurrentBatchConfig,fe=0,Be=null,Me=null,Ve=0,dt=0,nr=Xt(0),ze=0,qr=null,En=0,Xi=0,Sl=0,br=null,rt=null,xl=0,rr=1/0,Bt=null,Yi=!1,Cl=null,rn=null,Zi=!1,on=null,eo=0,Vr=0,El=null,to=-1,no=0;function Ze(){return(fe&6)!==0?Ie():to!==-1?to:to=Ie()}function sn(e){return(e.mode&1)===0?1:(fe&2)!==0&&Ve!==0?Ve&-Ve:ch.transition!==null?(no===0&&(no=Du()),no):(e=ve,e!==0||(e=window.event,e=e===void 0?16:Vu(e.type)),e)}function Rt(e,t,n,r){if(50<Vr)throw Vr=0,El=null,Error(s(185));mr(e,n,r),((fe&2)===0||e!==Be)&&(e===Be&&((fe&2)===0&&(Xi|=n),ze===4&&ln(e,Ve)),it(e,r),n===1&&fe===0&&(t.mode&1)===0&&(rr=Ie()+500,Ti&&Zt()))}function it(e,t){var n=e.callbackNode;cp(e,t);var r=fi(e,e===Be?Ve:0);if(r===0)n!==null&&ju(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ju(n),t===1)e.tag===0?ah(Vc.bind(null,e)):Ta(Vc.bind(null,e)),oh(function(){(fe&6)===0&&Zt()}),n=null;else{switch(Mu(r)){case 1:n=ns;break;case 4:n=Iu;break;case 16:n=li;break;case 536870912:n=Lu;break;default:n=li}n=Zc(n,bc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function bc(e,t){if(to=-1,no=0,(fe&6)!==0)throw Error(s(327));var n=e.callbackNode;if(ir()&&e.callbackNode!==n)return null;var r=fi(e,e===Be?Ve:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=ro(e,r);else{t=r;var u=fe;fe|=2;var c=Kc();(Be!==e||Ve!==t)&&(Bt=null,rr=Ie()+500,Rn(e,t));do try{Th();break}catch(m){Hc(e,m)}while(!0);$s(),Ji.current=c,fe=u,Me!==null?t=0:(Be=null,Ve=0,t=ze)}if(t!==0){if(t===2&&(u=rs(e),u!==0&&(r=u,t=kl(e,u))),t===1)throw n=qr,Rn(e,0),ln(e,r),it(e,Ie()),n;if(t===6)ln(e,r);else{if(u=e.current.alternate,(r&30)===0&&!Ph(u)&&(t=ro(e,r),t===2&&(c=rs(e),c!==0&&(r=c,t=kl(e,c))),t===1))throw n=qr,Rn(e,0),ln(e,r),it(e,Ie()),n;switch(e.finishedWork=u,e.finishedLanes=r,t){case 0:case 1:throw Error(s(345));case 2:_n(e,rt,Bt);break;case 3:if(ln(e,r),(r&130023424)===r&&(t=xl+500-Ie(),10<t)){if(fi(e,0)!==0)break;if(u=e.suspendedLanes,(u&r)!==r){Ze(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=Ts(_n.bind(null,e,rt,Bt),t);break}_n(e,rt,Bt);break;case 4:if(ln(e,r),(r&4194240)===r)break;for(t=e.eventTimes,u=-1;0<r;){var d=31-wt(r);c=1<<d,d=t[d],d>u&&(u=d),r&=~c}if(r=u,r=Ie()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Oh(r/1960))-r,10<r){e.timeoutHandle=Ts(_n.bind(null,e,rt,Bt),r);break}_n(e,rt,Bt);break;case 5:_n(e,rt,Bt);break;default:throw Error(s(329))}}}return it(e,Ie()),e.callbackNode===n?bc.bind(null,e):null}function kl(e,t){var n=br;return e.current.memoizedState.isDehydrated&&(Rn(e,t).flags|=256),e=ro(e,t),e!==2&&(t=rt,rt=n,t!==null&&Rl(t)),e}function Rl(e){rt===null?rt=e:rt.push.apply(rt,e)}function Ph(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var u=n[r],c=u.getSnapshot;u=u.value;try{if(!St(c(),u))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ln(e,t){for(t&=~Sl,t&=~Xi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-wt(t),r=1<<n;e[n]=-1,t&=~r}}function Vc(e){if((fe&6)!==0)throw Error(s(327));ir();var t=fi(e,0);if((t&1)===0)return it(e,Ie()),null;var n=ro(e,t);if(e.tag!==0&&n===2){var r=rs(e);r!==0&&(t=r,n=kl(e,r))}if(n===1)throw n=qr,Rn(e,0),ln(e,t),it(e,Ie()),n;if(n===6)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,_n(e,rt,Bt),it(e,Ie()),null}function _l(e,t){var n=fe;fe|=1;try{return e(t)}finally{fe=n,fe===0&&(rr=Ie()+500,Ti&&Zt())}}function kn(e){on!==null&&on.tag===0&&(fe&6)===0&&ir();var t=fe;fe|=1;var n=yt.transition,r=ve;try{if(yt.transition=null,ve=1,e)return e()}finally{ve=r,yt.transition=n,fe=t,(fe&6)===0&&Zt()}}function Ol(){dt=nr.current,Ce(nr)}function Rn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ih(n)),Me!==null)for(n=Me.return;n!==null;){var r=n;switch(Ds(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Pi();break;case 3:Zn(),Ce(et),Ce(He),Gs();break;case 5:Ks(r);break;case 4:Zn();break;case 13:Ce(Oe);break;case 19:Ce(Oe);break;case 10:Bs(r.type._context);break;case 22:case 23:Ol()}n=n.return}if(Be=e,Me=e=un(e.current,null),Ve=dt=t,ze=0,qr=null,Sl=Xi=En=0,rt=br=null,Sn!==null){for(t=0;t<Sn.length;t++)if(n=Sn[t],r=n.interleaved,r!==null){n.interleaved=null;var u=r.next,c=n.pending;if(c!==null){var d=c.next;c.next=u,r.next=d}n.pending=r}Sn=null}return e}function Hc(e,t){do{var n=Me;try{if($s(),Qi.current=bi,$i){for(var r=Pe.memoizedState;r!==null;){var u=r.queue;u!==null&&(u.pending=null),r=r.next}$i=!1}if(Cn=0,$e=Ue=Pe=null,Mr=!1,Ur=0,wl.current=null,n===null||n.return===null){ze=1,qr=t,Me=null;break}e:{var c=e,d=n.return,m=n,g=t;if(t=Ve,m.flags|=32768,g!==null&&typeof g=="object"&&typeof g.then=="function"){var O=g,L=m,D=L.tag;if((L.mode&1)===0&&(D===0||D===11||D===15)){var I=L.alternate;I?(L.updateQueue=I.updateQueue,L.memoizedState=I.memoizedState,L.lanes=I.lanes):(L.updateQueue=null,L.memoizedState=null)}var V=yc(d);if(V!==null){V.flags&=-257,gc(V,d,m,c,t),V.mode&1&&vc(c,O,t),t=V,g=O;var G=t.updateQueue;if(G===null){var J=new Set;J.add(g),t.updateQueue=J}else G.add(g);break e}else{if((t&1)===0){vc(c,O,t),Pl();break e}g=Error(s(426))}}else if(ke&&m.mode&1){var Le=yc(d);if(Le!==null){(Le.flags&65536)===0&&(Le.flags|=256),gc(Le,d,m,c,t),zs(er(g,m));break e}}c=g=er(g,m),ze!==4&&(ze=2),br===null?br=[c]:br.push(c),c=d;do{switch(c.tag){case 3:c.flags|=65536,t&=-t,c.lanes|=t;var C=hc(c,g,t);$a(c,C);break e;case 1:m=g;var S=c.type,R=c.stateNode;if((c.flags&128)===0&&(typeof S.getDerivedStateFromError=="function"||R!==null&&typeof R.componentDidCatch=="function"&&(rn===null||!rn.has(R)))){c.flags|=65536,t&=-t,c.lanes|=t;var M=mc(c,m,t);$a(c,M);break e}}c=c.return}while(c!==null)}Gc(n)}catch(Y){t=Y,Me===n&&n!==null&&(Me=n=n.return);continue}break}while(!0)}function Kc(){var e=Ji.current;return Ji.current=bi,e===null?bi:e}function Pl(){(ze===0||ze===3||ze===2)&&(ze=4),Be===null||(En&268435455)===0&&(Xi&268435455)===0||ln(Be,Ve)}function ro(e,t){var n=fe;fe|=2;var r=Kc();(Be!==e||Ve!==t)&&(Bt=null,Rn(e,t));do try{Nh();break}catch(u){Hc(e,u)}while(!0);if($s(),fe=n,Ji.current=r,Me!==null)throw Error(s(261));return Be=null,Ve=0,ze}function Nh(){for(;Me!==null;)Wc(Me)}function Th(){for(;Me!==null&&!tp();)Wc(Me)}function Wc(e){var t=Yc(e.alternate,e,dt);e.memoizedProps=e.pendingProps,t===null?Gc(e):Me=t,wl.current=null}function Gc(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=Ch(n,t,dt),n!==null){Me=n;return}}else{if(n=Eh(n,t),n!==null){n.flags&=32767,Me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ze=6,Me=null;return}}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);ze===0&&(ze=5)}function _n(e,t,n){var r=ve,u=yt.transition;try{yt.transition=null,ve=1,Fh(e,t,n,r)}finally{yt.transition=u,ve=r}return null}function Fh(e,t,n,r){do ir();while(on!==null);if((fe&6)!==0)throw Error(s(327));n=e.finishedWork;var u=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var c=n.lanes|n.childLanes;if(fp(e,c),e===Be&&(Me=Be=null,Ve=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||Zi||(Zi=!0,Zc(li,function(){return ir(),null})),c=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||c){c=yt.transition,yt.transition=null;var d=ve;ve=1;var m=fe;fe|=4,wl.current=null,Rh(e,n),zc(n,e),Xp(Ps),hi=!!Os,Ps=Os=null,e.current=n,_h(n),np(),fe=m,ve=d,yt.transition=c}else e.current=n;if(Zi&&(Zi=!1,on=e,eo=u),c=e.pendingLanes,c===0&&(rn=null),op(n.stateNode),it(e,Ie()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)u=t[n],r(u.value,{componentStack:u.stack,digest:u.digest});if(Yi)throw Yi=!1,e=Cl,Cl=null,e;return(eo&1)!==0&&e.tag!==0&&ir(),c=e.pendingLanes,(c&1)!==0?e===El?Vr++:(Vr=0,El=e):Vr=0,Zt(),null}function ir(){if(on!==null){var e=Mu(eo),t=yt.transition,n=ve;try{if(yt.transition=null,ve=16>e?16:e,on===null)var r=!1;else{if(e=on,on=null,eo=0,(fe&6)!==0)throw Error(s(331));var u=fe;for(fe|=4,H=e.current;H!==null;){var c=H,d=c.child;if((H.flags&16)!==0){var m=c.deletions;if(m!==null){for(var g=0;g<m.length;g++){var O=m[g];for(H=O;H!==null;){var L=H;switch(L.tag){case 0:case 11:case 15:Br(8,L,c)}var D=L.child;if(D!==null)D.return=L,H=D;else for(;H!==null;){L=H;var I=L.sibling,V=L.return;if(Ic(L),L===O){H=null;break}if(I!==null){I.return=V,H=I;break}H=V}}}var G=c.alternate;if(G!==null){var J=G.child;if(J!==null){G.child=null;do{var Le=J.sibling;J.sibling=null,J=Le}while(J!==null)}}H=c}}if((c.subtreeFlags&2064)!==0&&d!==null)d.return=c,H=d;else e:for(;H!==null;){if(c=H,(c.flags&2048)!==0)switch(c.tag){case 0:case 11:case 15:Br(9,c,c.return)}var C=c.sibling;if(C!==null){C.return=c.return,H=C;break e}H=c.return}}var S=e.current;for(H=S;H!==null;){d=H;var R=d.child;if((d.subtreeFlags&2064)!==0&&R!==null)R.return=d,H=R;else e:for(d=S;H!==null;){if(m=H,(m.flags&2048)!==0)try{switch(m.tag){case 0:case 11:case 15:Gi(9,m)}}catch(Y){Ae(m,m.return,Y)}if(m===d){H=null;break e}var M=m.sibling;if(M!==null){M.return=m.return,H=M;break e}H=m.return}}if(fe=u,Zt(),Pt&&typeof Pt.onPostCommitFiberRoot=="function")try{Pt.onPostCommitFiberRoot(ui,e)}catch{}r=!0}return r}finally{ve=n,yt.transition=t}}return!1}function Jc(e,t,n){t=er(n,t),t=hc(e,t,1),e=tn(e,t,1),t=Ze(),e!==null&&(mr(e,1,t),it(e,t))}function Ae(e,t,n){if(e.tag===3)Jc(e,e,n);else for(;t!==null;){if(t.tag===3){Jc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(rn===null||!rn.has(r))){e=er(n,e),e=mc(t,e,1),t=tn(t,e,1),e=Ze(),t!==null&&(mr(t,1,e),it(t,e));break}}t=t.return}}function Ah(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ze(),e.pingedLanes|=e.suspendedLanes&n,Be===e&&(Ve&n)===n&&(ze===4||ze===3&&(Ve&130023424)===Ve&&500>Ie()-xl?Rn(e,0):Sl|=n),it(e,t)}function Xc(e,t){t===0&&((e.mode&1)===0?t=1:(t=ci,ci<<=1,(ci&130023424)===0&&(ci=4194304)));var n=Ze();e=zt(e,t),e!==null&&(mr(e,t,n),it(e,n))}function jh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Xc(e,n)}function Ih(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,u=e.memoizedState;u!==null&&(n=u.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(s(314))}r!==null&&r.delete(t),Xc(e,n)}var Yc;Yc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||et.current)nt=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return nt=!1,xh(e,t,n);nt=(e.flags&131072)!==0}else nt=!1,ke&&(t.flags&1048576)!==0&&Fa(t,Ai,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ki(e,t),e=t.pendingProps;var u=Hn(t,He.current);Yn(t,n),u=Ys(null,t,r,e,u,n);var c=Zs();return t.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,tt(r)?(c=!0,Ni(t)):c=!1,t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,Vs(t),u.updater=Vi,t.stateNode=u,u._reactInternals=t,ol(t,r,e,n),t=al(null,t,r,!0,c,n)):(t.tag=0,ke&&c&&Ls(t),Ye(null,t,u,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ki(e,t),e=t.pendingProps,u=r._init,r=u(r._payload),t.type=r,u=t.tag=Dh(r),e=Ct(r,e),u){case 0:t=ul(null,t,r,e,n);break e;case 1:t=kc(null,t,r,e,n);break e;case 11:t=wc(null,t,r,e,n);break e;case 14:t=Sc(null,t,r,Ct(r.type,e),n);break e}throw Error(s(306,r,""))}return t;case 0:return r=t.type,u=t.pendingProps,u=t.elementType===r?u:Ct(r,u),ul(e,t,r,u,n);case 1:return r=t.type,u=t.pendingProps,u=t.elementType===r?u:Ct(r,u),kc(e,t,r,u,n);case 3:e:{if(Rc(t),e===null)throw Error(s(387));r=t.pendingProps,c=t.memoizedState,u=c.element,Qa(e,t),Ui(t,r,null,n);var d=t.memoizedState;if(r=d.element,c.isDehydrated)if(c={element:r,isDehydrated:!1,cache:d.cache,pendingSuspenseBoundaries:d.pendingSuspenseBoundaries,transitions:d.transitions},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){u=er(Error(s(423)),t),t=_c(e,t,r,n,u);break e}else if(r!==u){u=er(Error(s(424)),t),t=_c(e,t,r,n,u);break e}else for(ft=Jt(t.stateNode.containerInfo.firstChild),ct=t,ke=!0,xt=null,n=Ua(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Gn(),r===u){t=$t(e,t,n);break e}Ye(e,t,r,n)}t=t.child}return t;case 5:return qa(t),e===null&&Us(t),r=t.type,u=t.pendingProps,c=e!==null?e.memoizedProps:null,d=u.children,Ns(r,u)?d=null:c!==null&&Ns(r,c)&&(t.flags|=32),Ec(e,t),Ye(e,t,d,n),t.child;case 6:return e===null&&Us(t),null;case 13:return Oc(e,t,n);case 4:return Hs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Jn(t,null,r,n):Ye(e,t,r,n),t.child;case 11:return r=t.type,u=t.pendingProps,u=t.elementType===r?u:Ct(r,u),wc(e,t,r,u,n);case 7:return Ye(e,t,t.pendingProps,n),t.child;case 8:return Ye(e,t,t.pendingProps.children,n),t.child;case 12:return Ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,u=t.pendingProps,c=t.memoizedProps,d=u.value,we(Li,r._currentValue),r._currentValue=d,c!==null)if(St(c.value,d)){if(c.children===u.children&&!et.current){t=$t(e,t,n);break e}}else for(c=t.child,c!==null&&(c.return=t);c!==null;){var m=c.dependencies;if(m!==null){d=c.child;for(var g=m.firstContext;g!==null;){if(g.context===r){if(c.tag===1){g=Qt(-1,n&-n),g.tag=2;var O=c.updateQueue;if(O!==null){O=O.shared;var L=O.pending;L===null?g.next=g:(g.next=L.next,L.next=g),O.pending=g}}c.lanes|=n,g=c.alternate,g!==null&&(g.lanes|=n),qs(c.return,n,t),m.lanes|=n;break}g=g.next}}else if(c.tag===10)d=c.type===t.type?null:c.child;else if(c.tag===18){if(d=c.return,d===null)throw Error(s(341));d.lanes|=n,m=d.alternate,m!==null&&(m.lanes|=n),qs(d,n,t),d=c.sibling}else d=c.child;if(d!==null)d.return=c;else for(d=c;d!==null;){if(d===t){d=null;break}if(c=d.sibling,c!==null){c.return=d.return,d=c;break}d=d.return}c=d}Ye(e,t,u.children,n),t=t.child}return t;case 9:return u=t.type,r=t.pendingProps.children,Yn(t,n),u=mt(u),r=r(u),t.flags|=1,Ye(e,t,r,n),t.child;case 14:return r=t.type,u=Ct(r,t.pendingProps),u=Ct(r.type,u),Sc(e,t,r,u,n);case 15:return xc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,u=t.pendingProps,u=t.elementType===r?u:Ct(r,u),Ki(e,t),t.tag=1,tt(r)?(e=!0,Ni(t)):e=!1,Yn(t,n),dc(t,r,u),ol(t,r,u,n),al(null,t,r,!0,e,n);case 19:return Nc(e,t,n);case 22:return Cc(e,t,n)}throw Error(s(156,t.tag))};function Zc(e,t){return Au(e,t)}function Lh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,n,r){return new Lh(e,t,n,r)}function Nl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Dh(e){if(typeof e=="function")return Nl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Te)return 11;if(e===Fe)return 14}return 2}function un(e,t){var n=e.alternate;return n===null?(n=gt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function io(e,t,n,r,u,c){var d=2;if(r=e,typeof e=="function")Nl(e)&&(d=1);else if(typeof e=="string")d=5;else e:switch(e){case ue:return On(n.children,u,c,t);case le:d=8,u|=8;break;case De:return e=gt(12,n,t,u|2),e.elementType=De,e.lanes=c,e;case Se:return e=gt(13,n,t,u),e.elementType=Se,e.lanes=c,e;case _e:return e=gt(19,n,t,u),e.elementType=_e,e.lanes=c,e;case ce:return oo(n,u,c,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ye:d=10;break e;case Qe:d=9;break e;case Te:d=11;break e;case Fe:d=14;break e;case ae:d=16,r=null;break e}throw Error(s(130,e==null?e:typeof e,""))}return t=gt(d,n,t,u),t.elementType=e,t.type=r,t.lanes=c,t}function On(e,t,n,r){return e=gt(7,e,r,t),e.lanes=n,e}function oo(e,t,n,r){return e=gt(22,e,r,t),e.elementType=ce,e.lanes=n,e.stateNode={isHidden:!1},e}function Tl(e,t,n){return e=gt(6,e,null,t),e.lanes=n,e}function Fl(e,t,n){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mh(e,t,n,r,u){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=is(0),this.expirationTimes=is(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=is(0),this.identifierPrefix=r,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function Al(e,t,n,r,u,c,d,m,g){return e=new Mh(e,t,n,m,g),t===1?(t=1,c===!0&&(t|=8)):t=0,c=gt(3,null,null,t),e.current=c,c.stateNode=e,c.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Vs(c),e}function Uh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Z,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ef(e){if(!e)return Yt;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(tt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(s(171))}if(e.tag===1){var n=e.type;if(tt(n))return Pa(e,n,t)}return t}function tf(e,t,n,r,u,c,d,m,g){return e=Al(n,r,!0,e,u,c,d,m,g),e.context=ef(null),n=e.current,r=Ze(),u=sn(n),c=Qt(r,u),c.callback=t??null,tn(n,c,u),e.current.lanes=u,mr(e,u,r),it(e,r),e}function so(e,t,n,r){var u=t.current,c=Ze(),d=sn(u);return n=ef(n),t.context===null?t.context=n:t.pendingContext=n,t=Qt(c,d),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=tn(u,t,d),e!==null&&(Rt(e,u,d,c),Mi(e,u,d)),d}function lo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function jl(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function zh(){return null}var rf=typeof reportError=="function"?reportError:function(e){console.error(e)};function Il(e){this._internalRoot=e}uo.prototype.render=Il.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));so(e,t,null,null)},uo.prototype.unmount=Il.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;kn(function(){so(null,e,null,null)}),t[Lt]=null}};function uo(e){this._internalRoot=e}uo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Kt.length&&t!==0&&t<Kt[n].priority;n++);Kt.splice(n,0,e),n===0&&qu(e)}};function Ll(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ao(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function of(){}function Qh(e,t,n,r,u){if(u){if(typeof r=="function"){var c=r;r=function(){var O=lo(d);c.call(O)}}var d=tf(t,r,e,0,null,!1,!1,"",of);return e._reactRootContainer=d,e[Lt]=d.current,Nr(e.nodeType===8?e.parentNode:e),kn(),d}for(;u=e.lastChild;)e.removeChild(u);if(typeof r=="function"){var m=r;r=function(){var O=lo(g);m.call(O)}}var g=Al(e,0,!1,null,null,!1,!1,"",of);return e._reactRootContainer=g,e[Lt]=g.current,Nr(e.nodeType===8?e.parentNode:e),kn(function(){so(t,g,n,r)}),g}function co(e,t,n,r,u){var c=n._reactRootContainer;if(c){var d=c;if(typeof u=="function"){var m=u;u=function(){var g=lo(d);m.call(g)}}so(t,d,e,u)}else d=Qh(n,t,e,u,r);return lo(d)}Uu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=hr(t.pendingLanes);n!==0&&(os(t,n|1),it(t,Ie()),(fe&6)===0&&(rr=Ie()+500,Zt()))}break;case 13:kn(function(){var r=zt(e,1);if(r!==null){var u=Ze();Rt(r,e,1,u)}}),jl(e,1)}},ss=function(e){if(e.tag===13){var t=zt(e,134217728);if(t!==null){var n=Ze();Rt(t,e,134217728,n)}jl(e,134217728)}},zu=function(e){if(e.tag===13){var t=sn(e),n=zt(e,t);if(n!==null){var r=Ze();Rt(n,e,t,r)}jl(e,t)}},Qu=function(){return ve},$u=function(e,t){var n=ve;try{return ve=e,t()}finally{ve=n}},Yo=function(e,t,n){switch(t){case"input":if(bo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var u=Oi(r);if(!u)throw Error(s(90));fu(r),bo(r,u)}}}break;case"textarea":vu(e,n);break;case"select":t=n.value,t!=null&&In(e,!!n.multiple,t,!1)}},Ru=_l,_u=kn;var $h={usingClientEntryPoint:!1,Events:[Ar,bn,Oi,Eu,ku,_l]},Hr={findFiberByHostInstance:vn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Bh={bundleType:Hr.bundleType,version:Hr.version,rendererPackageName:Hr.rendererPackageName,rendererConfig:Hr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Tu(e),e===null?null:e.stateNode},findFiberByHostInstance:Hr.findFiberByHostInstance||zh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fo.isDisabled&&fo.supportsFiber)try{ui=fo.inject(Bh),Pt=fo}catch{}}return ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=$h,ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ll(t))throw Error(s(200));return Uh(e,t,null,n)},ot.createRoot=function(e,t){if(!Ll(e))throw Error(s(299));var n=!1,r="",u=rf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(u=t.onRecoverableError)),t=Al(e,1,!1,null,null,n,!1,r,u),e[Lt]=t.current,Nr(e.nodeType===8?e.parentNode:e),new Il(t)},ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=Tu(t),e=e===null?null:e.stateNode,e},ot.flushSync=function(e){return kn(e)},ot.hydrate=function(e,t,n){if(!ao(t))throw Error(s(200));return co(null,e,t,!0,n)},ot.hydrateRoot=function(e,t,n){if(!Ll(e))throw Error(s(405));var r=n!=null&&n.hydratedSources||null,u=!1,c="",d=rf;if(n!=null&&(n.unstable_strictMode===!0&&(u=!0),n.identifierPrefix!==void 0&&(c=n.identifierPrefix),n.onRecoverableError!==void 0&&(d=n.onRecoverableError)),t=tf(t,null,e,1,n??null,u,!1,c,d),e[Lt]=t.current,Nr(e),r)for(e=0;e<r.length;e++)n=r[e],u=n._getVersion,u=u(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,u]:t.mutableSourceEagerHydrationData.push(n,u);return new uo(t)},ot.render=function(e,t,n){if(!ao(t))throw Error(s(200));return co(null,e,t,!1,n)},ot.unmountComponentAtNode=function(e){if(!ao(e))throw Error(s(40));return e._reactRootContainer?(kn(function(){co(null,null,e,!1,function(){e._reactRootContainer=null,e[Lt]=null})}),!0):!1},ot.unstable_batchedUpdates=_l,ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ao(n))throw Error(s(200));if(e==null||e._reactInternals===void 0)throw Error(s(38));return co(e,t,n,!1,r)},ot.version="18.3.1-next-f1338f8080-20240426",ot}var pf;function Bf(){if(pf)return Ul.exports;pf=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(o){console.error(o)}}return i(),Ul.exports=Jh(),Ul.exports}var hf;function Xh(){if(hf)return po;hf=1;var i=Bf();return po.createRoot=i.createRoot,po.hydrateRoot=i.hydrateRoot,po}var Yh=Xh();function Vl(i,o){return Vl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,l){return s.__proto__=l,s},Vl(i,o)}function Xr(i,o){i.prototype=Object.create(o.prototype),i.prototype.constructor=i,Vl(i,o)}var Yr=function(){function i(){this.listeners=[]}var o=i.prototype;return o.subscribe=function(l){var a=this,f=l||function(){};return this.listeners.push(f),this.onSubscribe(),function(){a.listeners=a.listeners.filter(function(p){return p!==f}),a.onUnsubscribe()}},o.hasListeners=function(){return this.listeners.length>0},o.onSubscribe=function(){},o.onUnsubscribe=function(){},i}();function he(){return he=Object.assign?Object.assign.bind():function(i){for(var o=1;o<arguments.length;o++){var s=arguments[o];for(var l in s)({}).hasOwnProperty.call(s,l)&&(i[l]=s[l])}return i},he.apply(null,arguments)}var Eo=typeof window>"u";function Je(){}function Zh(i,o){return typeof i=="function"?i(o):i}function Hl(i){return typeof i=="number"&&i>=0&&i!==1/0}function ko(i){return Array.isArray(i)?i:[i]}function qf(i,o){return Math.max(i+(o||0)-Date.now(),0)}function mo(i,o,s){return jo(i)?typeof o=="function"?he({},s,{queryKey:i,queryFn:o}):he({},o,{queryKey:i}):i}function hn(i,o,s){return jo(i)?[he({},o,{queryKey:i}),s]:[i||{},o]}function em(i,o){if(i===!0&&o===!0||i==null&&o==null)return"all";if(i===!1&&o===!1)return"none";var s=i??!o;return s?"active":"inactive"}function mf(i,o){var s=i.active,l=i.exact,a=i.fetching,f=i.inactive,p=i.predicate,h=i.queryKey,y=i.stale;if(jo(h)){if(l){if(o.queryHash!==iu(h,o.options))return!1}else if(!Ro(o.queryKey,h))return!1}var v=em(s,f);if(v==="none")return!1;if(v!=="all"){var w=o.isActive();if(v==="active"&&!w||v==="inactive"&&w)return!1}return!(typeof y=="boolean"&&o.isStale()!==y||typeof a=="boolean"&&o.isFetching()!==a||p&&!p(o))}function vf(i,o){var s=i.exact,l=i.fetching,a=i.predicate,f=i.mutationKey;if(jo(f)){if(!o.options.mutationKey)return!1;if(s){if(Tn(o.options.mutationKey)!==Tn(f))return!1}else if(!Ro(o.options.mutationKey,f))return!1}return!(typeof l=="boolean"&&o.state.status==="loading"!==l||a&&!a(o))}function iu(i,o){var s=(o==null?void 0:o.queryKeyHashFn)||Tn;return s(i)}function Tn(i){var o=ko(i);return tm(o)}function tm(i){return JSON.stringify(i,function(o,s){return Kl(s)?Object.keys(s).sort().reduce(function(l,a){return l[a]=s[a],l},{}):s})}function Ro(i,o){return bf(ko(i),ko(o))}function bf(i,o){return i===o?!0:typeof i!=typeof o?!1:i&&o&&typeof i=="object"&&typeof o=="object"?!Object.keys(o).some(function(s){return!bf(i[s],o[s])}):!1}function _o(i,o){if(i===o)return i;var s=Array.isArray(i)&&Array.isArray(o);if(s||Kl(i)&&Kl(o)){for(var l=s?i.length:Object.keys(i).length,a=s?o:Object.keys(o),f=a.length,p=s?[]:{},h=0,y=0;y<f;y++){var v=s?y:a[y];p[v]=_o(i[v],o[v]),p[v]===i[v]&&h++}return l===f&&h===l?i:p}return o}function nm(i,o){if(i&&!o||o&&!i)return!1;for(var s in i)if(i[s]!==o[s])return!1;return!0}function Kl(i){if(!yf(i))return!1;var o=i.constructor;if(typeof o>"u")return!0;var s=o.prototype;return!(!yf(s)||!s.hasOwnProperty("isPrototypeOf"))}function yf(i){return Object.prototype.toString.call(i)==="[object Object]"}function jo(i){return typeof i=="string"||Array.isArray(i)}function rm(i){return new Promise(function(o){setTimeout(o,i)})}function gf(i){Promise.resolve().then(i).catch(function(o){return setTimeout(function(){throw o})})}function Vf(){if(typeof AbortController=="function")return new AbortController}var im=function(i){Xr(o,i);function o(){var l;return l=i.call(this)||this,l.setup=function(a){var f;if(!Eo&&((f=window)!=null&&f.addEventListener)){var p=function(){return a()};return window.addEventListener("visibilitychange",p,!1),window.addEventListener("focus",p,!1),function(){window.removeEventListener("visibilitychange",p),window.removeEventListener("focus",p)}}},l}var s=o.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var a;(a=this.cleanup)==null||a.call(this),this.cleanup=void 0}},s.setEventListener=function(a){var f,p=this;this.setup=a,(f=this.cleanup)==null||f.call(this),this.cleanup=a(function(h){typeof h=="boolean"?p.setFocused(h):p.onFocus()})},s.setFocused=function(a){this.focused=a,a&&this.onFocus()},s.onFocus=function(){this.listeners.forEach(function(a){a()})},s.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},o}(Yr),Gr=new im,om=function(i){Xr(o,i);function o(){var l;return l=i.call(this)||this,l.setup=function(a){var f;if(!Eo&&((f=window)!=null&&f.addEventListener)){var p=function(){return a()};return window.addEventListener("online",p,!1),window.addEventListener("offline",p,!1),function(){window.removeEventListener("online",p),window.removeEventListener("offline",p)}}},l}var s=o.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var a;(a=this.cleanup)==null||a.call(this),this.cleanup=void 0}},s.setEventListener=function(a){var f,p=this;this.setup=a,(f=this.cleanup)==null||f.call(this),this.cleanup=a(function(h){typeof h=="boolean"?p.setOnline(h):p.onOnline()})},s.setOnline=function(a){this.online=a,a&&this.onOnline()},s.onOnline=function(){this.listeners.forEach(function(a){a()})},s.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},o}(Yr),vo=new om;function sm(i){return Math.min(1e3*Math.pow(2,i),3e4)}function Oo(i){return typeof(i==null?void 0:i.cancel)=="function"}var Hf=function(o){this.revert=o==null?void 0:o.revert,this.silent=o==null?void 0:o.silent};function yo(i){return i instanceof Hf}var Kf=function(o){var s=this,l=!1,a,f,p,h;this.abort=o.abort,this.cancel=function(F){return a==null?void 0:a(F)},this.cancelRetry=function(){l=!0},this.continueRetry=function(){l=!1},this.continue=function(){return f==null?void 0:f()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(F,j){p=F,h=j});var y=function(j){s.isResolved||(s.isResolved=!0,o.onSuccess==null||o.onSuccess(j),f==null||f(),p(j))},v=function(j){s.isResolved||(s.isResolved=!0,o.onError==null||o.onError(j),f==null||f(),h(j))},w=function(){return new Promise(function(j){f=j,s.isPaused=!0,o.onPause==null||o.onPause()}).then(function(){f=void 0,s.isPaused=!1,o.onContinue==null||o.onContinue()})},E=function F(){if(!s.isResolved){var j;try{j=o.fn()}catch(T){j=Promise.reject(T)}a=function(A){if(!s.isResolved&&(v(new Hf(A)),s.abort==null||s.abort(),Oo(j)))try{j.cancel()}catch{}},s.isTransportCancelable=Oo(j),Promise.resolve(j).then(y).catch(function(T){var A,N;if(!s.isResolved){var $=(A=o.retry)!=null?A:3,q=(N=o.retryDelay)!=null?N:sm,z=typeof q=="function"?q(s.failureCount,T):q,b=$===!0||typeof $=="number"&&s.failureCount<$||typeof $=="function"&&$(s.failureCount,T);if(l||!b){v(T);return}s.failureCount++,o.onFail==null||o.onFail(s.failureCount,T),rm(z).then(function(){if(!Gr.isFocused()||!vo.isOnline())return w()}).then(function(){l?v(T):F()})}})}};E()},lm=function(){function i(){this.queue=[],this.transactions=0,this.notifyFn=function(s){s()},this.batchNotifyFn=function(s){s()}}var o=i.prototype;return o.batch=function(l){var a;this.transactions++;try{a=l()}finally{this.transactions--,this.transactions||this.flush()}return a},o.schedule=function(l){var a=this;this.transactions?this.queue.push(l):gf(function(){a.notifyFn(l)})},o.batchCalls=function(l){var a=this;return function(){for(var f=arguments.length,p=new Array(f),h=0;h<f;h++)p[h]=arguments[h];a.schedule(function(){l.apply(void 0,p)})}},o.flush=function(){var l=this,a=this.queue;this.queue=[],a.length&&gf(function(){l.batchNotifyFn(function(){a.forEach(function(f){l.notifyFn(f)})})})},o.setNotifyFunction=function(l){this.notifyFn=l},o.setBatchNotifyFunction=function(l){this.batchNotifyFn=l},i}(),je=new lm,Wf=console;function Po(){return Wf}function um(i){Wf=i}var am=function(){function i(s){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=s.defaultOptions,this.setOptions(s.options),this.observers=[],this.cache=s.cache,this.queryKey=s.queryKey,this.queryHash=s.queryHash,this.initialState=s.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=s.meta,this.scheduleGc()}var o=i.prototype;return o.setOptions=function(l){var a;this.options=he({},this.defaultOptions,l),this.meta=l==null?void 0:l.meta,this.cacheTime=Math.max(this.cacheTime||0,(a=this.options.cacheTime)!=null?a:5*60*1e3)},o.setDefaultOptions=function(l){this.defaultOptions=l},o.scheduleGc=function(){var l=this;this.clearGcTimeout(),Hl(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){l.optionalRemove()},this.cacheTime))},o.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},o.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},o.setData=function(l,a){var f,p,h=this.state.data,y=Zh(l,h);return(f=(p=this.options).isDataEqual)!=null&&f.call(p,h,y)?y=h:this.options.structuralSharing!==!1&&(y=_o(h,y)),this.dispatch({data:y,type:"success",dataUpdatedAt:a==null?void 0:a.updatedAt}),y},o.setState=function(l,a){this.dispatch({type:"setState",state:l,setStateOptions:a})},o.cancel=function(l){var a,f=this.promise;return(a=this.retryer)==null||a.cancel(l),f?f.then(Je).catch(Je):Promise.resolve()},o.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},o.reset=function(){this.destroy(),this.setState(this.initialState)},o.isActive=function(){return this.observers.some(function(l){return l.options.enabled!==!1})},o.isFetching=function(){return this.state.isFetching},o.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(l){return l.getCurrentResult().isStale})},o.isStaleByTime=function(l){return l===void 0&&(l=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!qf(this.state.dataUpdatedAt,l)},o.onFocus=function(){var l,a=this.observers.find(function(f){return f.shouldFetchOnWindowFocus()});a&&a.refetch(),(l=this.retryer)==null||l.continue()},o.onOnline=function(){var l,a=this.observers.find(function(f){return f.shouldFetchOnReconnect()});a&&a.refetch(),(l=this.retryer)==null||l.continue()},o.addObserver=function(l){this.observers.indexOf(l)===-1&&(this.observers.push(l),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:l}))},o.removeObserver=function(l){this.observers.indexOf(l)!==-1&&(this.observers=this.observers.filter(function(a){return a!==l}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:l}))},o.getObserversCount=function(){return this.observers.length},o.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},o.fetch=function(l,a){var f=this,p,h,y;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(a!=null&&a.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var v;return(v=this.retryer)==null||v.continueRetry(),this.promise}}if(l&&this.setOptions(l),!this.options.queryFn){var w=this.observers.find(function(q){return q.options.queryFn});w&&this.setOptions(w.options)}var E=ko(this.queryKey),F=Vf(),j={queryKey:E,pageParam:void 0,meta:this.meta};Object.defineProperty(j,"signal",{enumerable:!0,get:function(){if(F)return f.abortSignalConsumed=!0,F.signal}});var T=function(){return f.options.queryFn?(f.abortSignalConsumed=!1,f.options.queryFn(j)):Promise.reject("Missing queryFn")},A={fetchOptions:a,options:this.options,queryKey:E,state:this.state,fetchFn:T,meta:this.meta};if((p=this.options.behavior)!=null&&p.onFetch){var N;(N=this.options.behavior)==null||N.onFetch(A)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((h=A.fetchOptions)==null?void 0:h.meta)){var $;this.dispatch({type:"fetch",meta:($=A.fetchOptions)==null?void 0:$.meta})}return this.retryer=new Kf({fn:A.fetchFn,abort:F==null||(y=F.abort)==null?void 0:y.bind(F),onSuccess:function(z){f.setData(z),f.cache.config.onSuccess==null||f.cache.config.onSuccess(z,f),f.cacheTime===0&&f.optionalRemove()},onError:function(z){yo(z)&&z.silent||f.dispatch({type:"error",error:z}),yo(z)||(f.cache.config.onError==null||f.cache.config.onError(z,f),Po().error(z)),f.cacheTime===0&&f.optionalRemove()},onFail:function(){f.dispatch({type:"failed"})},onPause:function(){f.dispatch({type:"pause"})},onContinue:function(){f.dispatch({type:"continue"})},retry:A.options.retry,retryDelay:A.options.retryDelay}),this.promise=this.retryer.promise,this.promise},o.dispatch=function(l){var a=this;this.state=this.reducer(this.state,l),je.batch(function(){a.observers.forEach(function(f){f.onQueryUpdate(l)}),a.cache.notify({query:a,type:"queryUpdated",action:l})})},o.getDefaultState=function(l){var a=typeof l.initialData=="function"?l.initialData():l.initialData,f=typeof l.initialData<"u",p=f?typeof l.initialDataUpdatedAt=="function"?l.initialDataUpdatedAt():l.initialDataUpdatedAt:0,h=typeof a<"u";return{data:a,dataUpdateCount:0,dataUpdatedAt:h?p??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:h?"success":"idle"}},o.reducer=function(l,a){var f,p;switch(a.type){case"failed":return he({},l,{fetchFailureCount:l.fetchFailureCount+1});case"pause":return he({},l,{isPaused:!0});case"continue":return he({},l,{isPaused:!1});case"fetch":return he({},l,{fetchFailureCount:0,fetchMeta:(f=a.meta)!=null?f:null,isFetching:!0,isPaused:!1},!l.dataUpdatedAt&&{error:null,status:"loading"});case"success":return he({},l,{data:a.data,dataUpdateCount:l.dataUpdateCount+1,dataUpdatedAt:(p=a.dataUpdatedAt)!=null?p:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var h=a.error;return yo(h)&&h.revert&&this.revertState?he({},this.revertState):he({},l,{error:h,errorUpdateCount:l.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:l.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return he({},l,{isInvalidated:!0});case"setState":return he({},l,a.state);default:return l}},i}(),cm=function(i){Xr(o,i);function o(l){var a;return a=i.call(this)||this,a.config=l||{},a.queries=[],a.queriesMap={},a}var s=o.prototype;return s.build=function(a,f,p){var h,y=f.queryKey,v=(h=f.queryHash)!=null?h:iu(y,f),w=this.get(v);return w||(w=new am({cache:this,queryKey:y,queryHash:v,options:a.defaultQueryOptions(f),state:p,defaultOptions:a.getQueryDefaults(y),meta:f.meta}),this.add(w)),w},s.add=function(a){this.queriesMap[a.queryHash]||(this.queriesMap[a.queryHash]=a,this.queries.push(a),this.notify({type:"queryAdded",query:a}))},s.remove=function(a){var f=this.queriesMap[a.queryHash];f&&(a.destroy(),this.queries=this.queries.filter(function(p){return p!==a}),f===a&&delete this.queriesMap[a.queryHash],this.notify({type:"queryRemoved",query:a}))},s.clear=function(){var a=this;je.batch(function(){a.queries.forEach(function(f){a.remove(f)})})},s.get=function(a){return this.queriesMap[a]},s.getAll=function(){return this.queries},s.find=function(a,f){var p=hn(a,f),h=p[0];return typeof h.exact>"u"&&(h.exact=!0),this.queries.find(function(y){return mf(h,y)})},s.findAll=function(a,f){var p=hn(a,f),h=p[0];return Object.keys(h).length>0?this.queries.filter(function(y){return mf(h,y)}):this.queries},s.notify=function(a){var f=this;je.batch(function(){f.listeners.forEach(function(p){p(a)})})},s.onFocus=function(){var a=this;je.batch(function(){a.queries.forEach(function(f){f.onFocus()})})},s.onOnline=function(){var a=this;je.batch(function(){a.queries.forEach(function(f){f.onOnline()})})},o}(Yr),fm=function(){function i(s){this.options=he({},s.defaultOptions,s.options),this.mutationId=s.mutationId,this.mutationCache=s.mutationCache,this.observers=[],this.state=s.state||dm(),this.meta=s.meta}var o=i.prototype;return o.setState=function(l){this.dispatch({type:"setState",state:l})},o.addObserver=function(l){this.observers.indexOf(l)===-1&&this.observers.push(l)},o.removeObserver=function(l){this.observers=this.observers.filter(function(a){return a!==l})},o.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Je).catch(Je)):Promise.resolve()},o.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},o.execute=function(){var l=this,a,f=this.state.status==="loading",p=Promise.resolve();return f||(this.dispatch({type:"loading",variables:this.options.variables}),p=p.then(function(){l.mutationCache.config.onMutate==null||l.mutationCache.config.onMutate(l.state.variables,l)}).then(function(){return l.options.onMutate==null?void 0:l.options.onMutate(l.state.variables)}).then(function(h){h!==l.state.context&&l.dispatch({type:"loading",context:h,variables:l.state.variables})})),p.then(function(){return l.executeMutation()}).then(function(h){a=h,l.mutationCache.config.onSuccess==null||l.mutationCache.config.onSuccess(a,l.state.variables,l.state.context,l)}).then(function(){return l.options.onSuccess==null?void 0:l.options.onSuccess(a,l.state.variables,l.state.context)}).then(function(){return l.options.onSettled==null?void 0:l.options.onSettled(a,null,l.state.variables,l.state.context)}).then(function(){return l.dispatch({type:"success",data:a}),a}).catch(function(h){return l.mutationCache.config.onError==null||l.mutationCache.config.onError(h,l.state.variables,l.state.context,l),Po().error(h),Promise.resolve().then(function(){return l.options.onError==null?void 0:l.options.onError(h,l.state.variables,l.state.context)}).then(function(){return l.options.onSettled==null?void 0:l.options.onSettled(void 0,h,l.state.variables,l.state.context)}).then(function(){throw l.dispatch({type:"error",error:h}),h})})},o.executeMutation=function(){var l=this,a;return this.retryer=new Kf({fn:function(){return l.options.mutationFn?l.options.mutationFn(l.state.variables):Promise.reject("No mutationFn found")},onFail:function(){l.dispatch({type:"failed"})},onPause:function(){l.dispatch({type:"pause"})},onContinue:function(){l.dispatch({type:"continue"})},retry:(a=this.options.retry)!=null?a:0,retryDelay:this.options.retryDelay}),this.retryer.promise},o.dispatch=function(l){var a=this;this.state=pm(this.state,l),je.batch(function(){a.observers.forEach(function(f){f.onMutationUpdate(l)}),a.mutationCache.notify(a)})},i}();function dm(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function pm(i,o){switch(o.type){case"failed":return he({},i,{failureCount:i.failureCount+1});case"pause":return he({},i,{isPaused:!0});case"continue":return he({},i,{isPaused:!1});case"loading":return he({},i,{context:o.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:o.variables});case"success":return he({},i,{data:o.data,error:null,status:"success",isPaused:!1});case"error":return he({},i,{data:void 0,error:o.error,failureCount:i.failureCount+1,isPaused:!1,status:"error"});case"setState":return he({},i,o.state);default:return i}}var hm=function(i){Xr(o,i);function o(l){var a;return a=i.call(this)||this,a.config=l||{},a.mutations=[],a.mutationId=0,a}var s=o.prototype;return s.build=function(a,f,p){var h=new fm({mutationCache:this,mutationId:++this.mutationId,options:a.defaultMutationOptions(f),state:p,defaultOptions:f.mutationKey?a.getMutationDefaults(f.mutationKey):void 0,meta:f.meta});return this.add(h),h},s.add=function(a){this.mutations.push(a),this.notify(a)},s.remove=function(a){this.mutations=this.mutations.filter(function(f){return f!==a}),a.cancel(),this.notify(a)},s.clear=function(){var a=this;je.batch(function(){a.mutations.forEach(function(f){a.remove(f)})})},s.getAll=function(){return this.mutations},s.find=function(a){return typeof a.exact>"u"&&(a.exact=!0),this.mutations.find(function(f){return vf(a,f)})},s.findAll=function(a){return this.mutations.filter(function(f){return vf(a,f)})},s.notify=function(a){var f=this;je.batch(function(){f.listeners.forEach(function(p){p(a)})})},s.onFocus=function(){this.resumePausedMutations()},s.onOnline=function(){this.resumePausedMutations()},s.resumePausedMutations=function(){var a=this.mutations.filter(function(f){return f.state.isPaused});return je.batch(function(){return a.reduce(function(f,p){return f.then(function(){return p.continue().catch(Je)})},Promise.resolve())})},o}(Yr);function mm(){return{onFetch:function(o){o.fetchFn=function(){var s,l,a,f,p,h,y=(s=o.fetchOptions)==null||(l=s.meta)==null?void 0:l.refetchPage,v=(a=o.fetchOptions)==null||(f=a.meta)==null?void 0:f.fetchMore,w=v==null?void 0:v.pageParam,E=(v==null?void 0:v.direction)==="forward",F=(v==null?void 0:v.direction)==="backward",j=((p=o.state.data)==null?void 0:p.pages)||[],T=((h=o.state.data)==null?void 0:h.pageParams)||[],A=Vf(),N=A==null?void 0:A.signal,$=T,q=!1,z=o.options.queryFn||function(){return Promise.reject("Missing queryFn")},b=function(_e,Fe,ae,ce){return $=ce?[Fe].concat($):[].concat($,[Fe]),ce?[ae].concat(_e):[].concat(_e,[ae])},ie=function(_e,Fe,ae,ce){if(q)return Promise.reject("Cancelled");if(typeof ae>"u"&&!Fe&&_e.length)return Promise.resolve(_e);var Q={queryKey:o.queryKey,signal:N,pageParam:ae,meta:o.meta},X=z(Q),B=Promise.resolve(X).then(function(k){return b(_e,ae,k,ce)});if(Oo(X)){var x=B;x.cancel=X.cancel}return B},Z;if(!j.length)Z=ie([]);else if(E){var ue=typeof w<"u",le=ue?w:wf(o.options,j);Z=ie(j,ue,le)}else if(F){var De=typeof w<"u",ye=De?w:vm(o.options,j);Z=ie(j,De,ye,!0)}else(function(){$=[];var Se=typeof o.options.getNextPageParam>"u",_e=y&&j[0]?y(j[0],0,j):!0;Z=_e?ie([],Se,T[0]):Promise.resolve(b([],T[0],j[0]));for(var Fe=function(Q){Z=Z.then(function(X){var B=y&&j[Q]?y(j[Q],Q,j):!0;if(B){var x=Se?T[Q]:wf(o.options,X);return ie(X,Se,x)}return Promise.resolve(b(X,T[Q],j[Q]))})},ae=1;ae<j.length;ae++)Fe(ae)})();var Qe=Z.then(function(Se){return{pages:Se,pageParams:$}}),Te=Qe;return Te.cancel=function(){q=!0,A==null||A.abort(),Oo(Z)&&Z.cancel()},Qe}}}}function wf(i,o){return i.getNextPageParam==null?void 0:i.getNextPageParam(o[o.length-1],o)}function vm(i,o){return i.getPreviousPageParam==null?void 0:i.getPreviousPageParam(o[0],o)}var ym=function(){function i(s){s===void 0&&(s={}),this.queryCache=s.queryCache||new cm,this.mutationCache=s.mutationCache||new hm,this.defaultOptions=s.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var o=i.prototype;return o.mount=function(){var l=this;this.unsubscribeFocus=Gr.subscribe(function(){Gr.isFocused()&&vo.isOnline()&&(l.mutationCache.onFocus(),l.queryCache.onFocus())}),this.unsubscribeOnline=vo.subscribe(function(){Gr.isFocused()&&vo.isOnline()&&(l.mutationCache.onOnline(),l.queryCache.onOnline())})},o.unmount=function(){var l,a;(l=this.unsubscribeFocus)==null||l.call(this),(a=this.unsubscribeOnline)==null||a.call(this)},o.isFetching=function(l,a){var f=hn(l,a),p=f[0];return p.fetching=!0,this.queryCache.findAll(p).length},o.isMutating=function(l){return this.mutationCache.findAll(he({},l,{fetching:!0})).length},o.getQueryData=function(l,a){var f;return(f=this.queryCache.find(l,a))==null?void 0:f.state.data},o.getQueriesData=function(l){return this.getQueryCache().findAll(l).map(function(a){var f=a.queryKey,p=a.state,h=p.data;return[f,h]})},o.setQueryData=function(l,a,f){var p=mo(l),h=this.defaultQueryOptions(p);return this.queryCache.build(this,h).setData(a,f)},o.setQueriesData=function(l,a,f){var p=this;return je.batch(function(){return p.getQueryCache().findAll(l).map(function(h){var y=h.queryKey;return[y,p.setQueryData(y,a,f)]})})},o.getQueryState=function(l,a){var f;return(f=this.queryCache.find(l,a))==null?void 0:f.state},o.removeQueries=function(l,a){var f=hn(l,a),p=f[0],h=this.queryCache;je.batch(function(){h.findAll(p).forEach(function(y){h.remove(y)})})},o.resetQueries=function(l,a,f){var p=this,h=hn(l,a,f),y=h[0],v=h[1],w=this.queryCache,E=he({},y,{active:!0});return je.batch(function(){return w.findAll(y).forEach(function(F){F.reset()}),p.refetchQueries(E,v)})},o.cancelQueries=function(l,a,f){var p=this,h=hn(l,a,f),y=h[0],v=h[1],w=v===void 0?{}:v;typeof w.revert>"u"&&(w.revert=!0);var E=je.batch(function(){return p.queryCache.findAll(y).map(function(F){return F.cancel(w)})});return Promise.all(E).then(Je).catch(Je)},o.invalidateQueries=function(l,a,f){var p,h,y,v=this,w=hn(l,a,f),E=w[0],F=w[1],j=he({},E,{active:(p=(h=E.refetchActive)!=null?h:E.active)!=null?p:!0,inactive:(y=E.refetchInactive)!=null?y:!1});return je.batch(function(){return v.queryCache.findAll(E).forEach(function(T){T.invalidate()}),v.refetchQueries(j,F)})},o.refetchQueries=function(l,a,f){var p=this,h=hn(l,a,f),y=h[0],v=h[1],w=je.batch(function(){return p.queryCache.findAll(y).map(function(F){return F.fetch(void 0,he({},v,{meta:{refetchPage:y==null?void 0:y.refetchPage}}))})}),E=Promise.all(w).then(Je);return v!=null&&v.throwOnError||(E=E.catch(Je)),E},o.fetchQuery=function(l,a,f){var p=mo(l,a,f),h=this.defaultQueryOptions(p);typeof h.retry>"u"&&(h.retry=!1);var y=this.queryCache.build(this,h);return y.isStaleByTime(h.staleTime)?y.fetch(h):Promise.resolve(y.state.data)},o.prefetchQuery=function(l,a,f){return this.fetchQuery(l,a,f).then(Je).catch(Je)},o.fetchInfiniteQuery=function(l,a,f){var p=mo(l,a,f);return p.behavior=mm(),this.fetchQuery(p)},o.prefetchInfiniteQuery=function(l,a,f){return this.fetchInfiniteQuery(l,a,f).then(Je).catch(Je)},o.cancelMutations=function(){var l=this,a=je.batch(function(){return l.mutationCache.getAll().map(function(f){return f.cancel()})});return Promise.all(a).then(Je).catch(Je)},o.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},o.executeMutation=function(l){return this.mutationCache.build(this,l).execute()},o.getQueryCache=function(){return this.queryCache},o.getMutationCache=function(){return this.mutationCache},o.getDefaultOptions=function(){return this.defaultOptions},o.setDefaultOptions=function(l){this.defaultOptions=l},o.setQueryDefaults=function(l,a){var f=this.queryDefaults.find(function(p){return Tn(l)===Tn(p.queryKey)});f?f.defaultOptions=a:this.queryDefaults.push({queryKey:l,defaultOptions:a})},o.getQueryDefaults=function(l){var a;return l?(a=this.queryDefaults.find(function(f){return Ro(l,f.queryKey)}))==null?void 0:a.defaultOptions:void 0},o.setMutationDefaults=function(l,a){var f=this.mutationDefaults.find(function(p){return Tn(l)===Tn(p.mutationKey)});f?f.defaultOptions=a:this.mutationDefaults.push({mutationKey:l,defaultOptions:a})},o.getMutationDefaults=function(l){var a;return l?(a=this.mutationDefaults.find(function(f){return Ro(l,f.mutationKey)}))==null?void 0:a.defaultOptions:void 0},o.defaultQueryOptions=function(l){if(l!=null&&l._defaulted)return l;var a=he({},this.defaultOptions.queries,this.getQueryDefaults(l==null?void 0:l.queryKey),l,{_defaulted:!0});return!a.queryHash&&a.queryKey&&(a.queryHash=iu(a.queryKey,a)),a},o.defaultQueryObserverOptions=function(l){return this.defaultQueryOptions(l)},o.defaultMutationOptions=function(l){return l!=null&&l._defaulted?l:he({},this.defaultOptions.mutations,this.getMutationDefaults(l==null?void 0:l.mutationKey),l,{_defaulted:!0})},o.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},i}(),gm=function(i){Xr(o,i);function o(l,a){var f;return f=i.call(this)||this,f.client=l,f.options=a,f.trackedProps=[],f.selectError=null,f.bindMethods(),f.setOptions(a),f}var s=o.prototype;return s.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},s.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Sf(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},s.onUnsubscribe=function(){this.listeners.length||this.destroy()},s.shouldFetchOnReconnect=function(){return Wl(this.currentQuery,this.options,this.options.refetchOnReconnect)},s.shouldFetchOnWindowFocus=function(){return Wl(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},s.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},s.setOptions=function(a,f){var p=this.options,h=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(a),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=p.queryKey),this.updateQuery();var y=this.hasListeners();y&&xf(this.currentQuery,h,this.options,p)&&this.executeFetch(),this.updateResult(f),y&&(this.currentQuery!==h||this.options.enabled!==p.enabled||this.options.staleTime!==p.staleTime)&&this.updateStaleTimeout();var v=this.computeRefetchInterval();y&&(this.currentQuery!==h||this.options.enabled!==p.enabled||v!==this.currentRefetchInterval)&&this.updateRefetchInterval(v)},s.getOptimisticResult=function(a){var f=this.client.defaultQueryObserverOptions(a),p=this.client.getQueryCache().build(this.client,f);return this.createResult(p,f)},s.getCurrentResult=function(){return this.currentResult},s.trackResult=function(a,f){var p=this,h={},y=function(w){p.trackedProps.includes(w)||p.trackedProps.push(w)};return Object.keys(a).forEach(function(v){Object.defineProperty(h,v,{configurable:!1,enumerable:!0,get:function(){return y(v),a[v]}})}),(f.useErrorBoundary||f.suspense)&&y("error"),h},s.getNextResult=function(a){var f=this;return new Promise(function(p,h){var y=f.subscribe(function(v){v.isFetching||(y(),v.isError&&(a!=null&&a.throwOnError)?h(v.error):p(v))})})},s.getCurrentQuery=function(){return this.currentQuery},s.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},s.refetch=function(a){return this.fetch(he({},a,{meta:{refetchPage:a==null?void 0:a.refetchPage}}))},s.fetchOptimistic=function(a){var f=this,p=this.client.defaultQueryObserverOptions(a),h=this.client.getQueryCache().build(this.client,p);return h.fetch().then(function(){return f.createResult(h,p)})},s.fetch=function(a){var f=this;return this.executeFetch(a).then(function(){return f.updateResult(),f.currentResult})},s.executeFetch=function(a){this.updateQuery();var f=this.currentQuery.fetch(this.options,a);return a!=null&&a.throwOnError||(f=f.catch(Je)),f},s.updateStaleTimeout=function(){var a=this;if(this.clearStaleTimeout(),!(Eo||this.currentResult.isStale||!Hl(this.options.staleTime))){var f=qf(this.currentResult.dataUpdatedAt,this.options.staleTime),p=f+1;this.staleTimeoutId=setTimeout(function(){a.currentResult.isStale||a.updateResult()},p)}},s.computeRefetchInterval=function(){var a;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(a=this.options.refetchInterval)!=null?a:!1},s.updateRefetchInterval=function(a){var f=this;this.clearRefetchInterval(),this.currentRefetchInterval=a,!(Eo||this.options.enabled===!1||!Hl(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(f.options.refetchIntervalInBackground||Gr.isFocused())&&f.executeFetch()},this.currentRefetchInterval))},s.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},s.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},s.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},s.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},s.createResult=function(a,f){var p=this.currentQuery,h=this.options,y=this.currentResult,v=this.currentResultState,w=this.currentResultOptions,E=a!==p,F=E?a.state:this.currentQueryInitialState,j=E?this.currentResult:this.previousQueryResult,T=a.state,A=T.dataUpdatedAt,N=T.error,$=T.errorUpdatedAt,q=T.isFetching,z=T.status,b=!1,ie=!1,Z;if(f.optimisticResults){var ue=this.hasListeners(),le=!ue&&Sf(a,f),De=ue&&xf(a,p,f,h);(le||De)&&(q=!0,A||(z="loading"))}if(f.keepPreviousData&&!T.dataUpdateCount&&(j!=null&&j.isSuccess)&&z!=="error")Z=j.data,A=j.dataUpdatedAt,z=j.status,b=!0;else if(f.select&&typeof T.data<"u")if(y&&T.data===(v==null?void 0:v.data)&&f.select===this.selectFn)Z=this.selectResult;else try{this.selectFn=f.select,Z=f.select(T.data),f.structuralSharing!==!1&&(Z=_o(y==null?void 0:y.data,Z)),this.selectResult=Z,this.selectError=null}catch(Te){Po().error(Te),this.selectError=Te}else Z=T.data;if(typeof f.placeholderData<"u"&&typeof Z>"u"&&(z==="loading"||z==="idle")){var ye;if(y!=null&&y.isPlaceholderData&&f.placeholderData===(w==null?void 0:w.placeholderData))ye=y.data;else if(ye=typeof f.placeholderData=="function"?f.placeholderData():f.placeholderData,f.select&&typeof ye<"u")try{ye=f.select(ye),f.structuralSharing!==!1&&(ye=_o(y==null?void 0:y.data,ye)),this.selectError=null}catch(Te){Po().error(Te),this.selectError=Te}typeof ye<"u"&&(z="success",Z=ye,ie=!0)}this.selectError&&(N=this.selectError,Z=this.selectResult,$=Date.now(),z="error");var Qe={status:z,isLoading:z==="loading",isSuccess:z==="success",isError:z==="error",isIdle:z==="idle",data:Z,dataUpdatedAt:A,error:N,errorUpdatedAt:$,failureCount:T.fetchFailureCount,errorUpdateCount:T.errorUpdateCount,isFetched:T.dataUpdateCount>0||T.errorUpdateCount>0,isFetchedAfterMount:T.dataUpdateCount>F.dataUpdateCount||T.errorUpdateCount>F.errorUpdateCount,isFetching:q,isRefetching:q&&z!=="loading",isLoadingError:z==="error"&&T.dataUpdatedAt===0,isPlaceholderData:ie,isPreviousData:b,isRefetchError:z==="error"&&T.dataUpdatedAt!==0,isStale:ou(a,f),refetch:this.refetch,remove:this.remove};return Qe},s.shouldNotifyListeners=function(a,f){if(!f)return!0;var p=this.options,h=p.notifyOnChangeProps,y=p.notifyOnChangePropsExclusions;if(!h&&!y||h==="tracked"&&!this.trackedProps.length)return!0;var v=h==="tracked"?this.trackedProps:h;return Object.keys(a).some(function(w){var E=w,F=a[E]!==f[E],j=v==null?void 0:v.some(function(A){return A===w}),T=y==null?void 0:y.some(function(A){return A===w});return F&&!T&&(!v||j)})},s.updateResult=function(a){var f=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!nm(this.currentResult,f)){var p={cache:!0};(a==null?void 0:a.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,f)&&(p.listeners=!0),this.notify(he({},p,a))}},s.updateQuery=function(){var a=this.client.getQueryCache().build(this.client,this.options);if(a!==this.currentQuery){var f=this.currentQuery;this.currentQuery=a,this.currentQueryInitialState=a.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(f==null||f.removeObserver(this),a.addObserver(this))}},s.onQueryUpdate=function(a){var f={};a.type==="success"?f.onSuccess=!0:a.type==="error"&&!yo(a.error)&&(f.onError=!0),this.updateResult(f),this.hasListeners()&&this.updateTimers()},s.notify=function(a){var f=this;je.batch(function(){a.onSuccess?(f.options.onSuccess==null||f.options.onSuccess(f.currentResult.data),f.options.onSettled==null||f.options.onSettled(f.currentResult.data,null)):a.onError&&(f.options.onError==null||f.options.onError(f.currentResult.error),f.options.onSettled==null||f.options.onSettled(void 0,f.currentResult.error)),a.listeners&&f.listeners.forEach(function(p){p(f.currentResult)}),a.cache&&f.client.getQueryCache().notify({query:f.currentQuery,type:"observerResultsUpdated"})})},o}(Yr);function wm(i,o){return o.enabled!==!1&&!i.state.dataUpdatedAt&&!(i.state.status==="error"&&o.retryOnMount===!1)}function Sf(i,o){return wm(i,o)||i.state.dataUpdatedAt>0&&Wl(i,o,o.refetchOnMount)}function Wl(i,o,s){if(o.enabled!==!1){var l=typeof s=="function"?s(i):s;return l==="always"||l!==!1&&ou(i,o)}return!1}function xf(i,o,s,l){return s.enabled!==!1&&(i!==o||l.enabled===!1)&&(!s.suspense||i.state.status!=="error")&&ou(i,s)}function ou(i,o){return i.isStaleByTime(o.staleTime)}var Sm=Bf();const xm=Qf(Sm);var Cm=xm.unstable_batchedUpdates;je.setBatchNotifyFunction(Cm);var Em=console;um(Em);var Cf=Ne.createContext(void 0),Gf=Ne.createContext(!1);function Jf(i){return i&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Cf),window.ReactQueryClientContext):Cf}var km=function(){var o=Ne.useContext(Jf(Ne.useContext(Gf)));if(!o)throw new Error("No QueryClient set, use QueryClientProvider to set one");return o},Rm=function(o){var s=o.client,l=o.contextSharing,a=l===void 0?!1:l,f=o.children;Ne.useEffect(function(){return s.mount(),function(){s.unmount()}},[s]);var p=Jf(a);return Ne.createElement(Gf.Provider,{value:a},Ne.createElement(p.Provider,{value:s},f))};function _m(){var i=!1;return{clearReset:function(){i=!1},reset:function(){i=!0},isReset:function(){return i}}}var Om=Ne.createContext(_m()),Pm=function(){return Ne.useContext(Om)};function Nm(i,o,s){return typeof o=="function"?o.apply(void 0,s):typeof o=="boolean"?o:!!i}function Tm(i,o){var s=Ne.useRef(!1),l=Ne.useState(0),a=l[1],f=km(),p=Pm(),h=f.defaultQueryObserverOptions(i);h.optimisticResults=!0,h.onError&&(h.onError=je.batchCalls(h.onError)),h.onSuccess&&(h.onSuccess=je.batchCalls(h.onSuccess)),h.onSettled&&(h.onSettled=je.batchCalls(h.onSettled)),h.suspense&&(typeof h.staleTime!="number"&&(h.staleTime=1e3),h.cacheTime===0&&(h.cacheTime=1)),(h.suspense||h.useErrorBoundary)&&(p.isReset()||(h.retryOnMount=!1));var y=Ne.useState(function(){return new o(f,h)}),v=y[0],w=v.getOptimisticResult(h);if(Ne.useEffect(function(){s.current=!0,p.clearReset();var E=v.subscribe(je.batchCalls(function(){s.current&&a(function(F){return F+1})}));return v.updateResult(),function(){s.current=!1,E()}},[p,v]),Ne.useEffect(function(){v.setOptions(h,{listeners:!1})},[h,v]),h.suspense&&w.isLoading)throw v.fetchOptimistic(h).then(function(E){var F=E.data;h.onSuccess==null||h.onSuccess(F),h.onSettled==null||h.onSettled(F,null)}).catch(function(E){p.clearReset(),h.onError==null||h.onError(E),h.onSettled==null||h.onSettled(void 0,E)});if(w.isError&&!p.isReset()&&!w.isFetching&&Nm(h.suspense,h.useErrorBoundary,[w.error,v.getCurrentQuery()]))throw w.error;return h.notifyOnChangeProps==="tracked"&&(w=v.trackResult(w,h)),w}function Fm(i,o,s){var l=mo(i,o,s);return Tm(l,gm)}function Xf(i,o){return function(){return i.apply(o,arguments)}}const{toString:Am}=Object.prototype,{getPrototypeOf:su}=Object,{iterator:Io,toStringTag:Yf}=Symbol,Lo=(i=>o=>{const s=Am.call(o);return i[s]||(i[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),Ot=i=>(i=i.toLowerCase(),o=>Lo(o)===i),Do=i=>o=>typeof o===i,{isArray:or}=Array,Jr=Do("undefined");function jm(i){return i!==null&&!Jr(i)&&i.constructor!==null&&!Jr(i.constructor)&&st(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const Zf=Ot("ArrayBuffer");function Im(i){let o;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?o=ArrayBuffer.isView(i):o=i&&i.buffer&&Zf(i.buffer),o}const Lm=Do("string"),st=Do("function"),ed=Do("number"),Mo=i=>i!==null&&typeof i=="object",Dm=i=>i===!0||i===!1,go=i=>{if(Lo(i)!=="object")return!1;const o=su(i);return(o===null||o===Object.prototype||Object.getPrototypeOf(o)===null)&&!(Yf in i)&&!(Io in i)},Mm=Ot("Date"),Um=Ot("File"),zm=Ot("Blob"),Qm=Ot("FileList"),$m=i=>Mo(i)&&st(i.pipe),Bm=i=>{let o;return i&&(typeof FormData=="function"&&i instanceof FormData||st(i.append)&&((o=Lo(i))==="formdata"||o==="object"&&st(i.toString)&&i.toString()==="[object FormData]"))},qm=Ot("URLSearchParams"),[bm,Vm,Hm,Km]=["ReadableStream","Request","Response","Headers"].map(Ot),Wm=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Zr(i,o,{allOwnKeys:s=!1}={}){if(i===null||typeof i>"u")return;let l,a;if(typeof i!="object"&&(i=[i]),or(i))for(l=0,a=i.length;l<a;l++)o.call(null,i[l],l,i);else{const f=s?Object.getOwnPropertyNames(i):Object.keys(i),p=f.length;let h;for(l=0;l<p;l++)h=f[l],o.call(null,i[h],h,i)}}function td(i,o){o=o.toLowerCase();const s=Object.keys(i);let l=s.length,a;for(;l-- >0;)if(a=s[l],o===a.toLowerCase())return a;return null}const Fn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,nd=i=>!Jr(i)&&i!==Fn;function Gl(){const{caseless:i}=nd(this)&&this||{},o={},s=(l,a)=>{const f=i&&td(o,a)||a;go(o[f])&&go(l)?o[f]=Gl(o[f],l):go(l)?o[f]=Gl({},l):or(l)?o[f]=l.slice():o[f]=l};for(let l=0,a=arguments.length;l<a;l++)arguments[l]&&Zr(arguments[l],s);return o}const Gm=(i,o,s,{allOwnKeys:l}={})=>(Zr(o,(a,f)=>{s&&st(a)?i[f]=Xf(a,s):i[f]=a},{allOwnKeys:l}),i),Jm=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),Xm=(i,o,s,l)=>{i.prototype=Object.create(o.prototype,l),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:o.prototype}),s&&Object.assign(i.prototype,s)},Ym=(i,o,s,l)=>{let a,f,p;const h={};if(o=o||{},i==null)return o;do{for(a=Object.getOwnPropertyNames(i),f=a.length;f-- >0;)p=a[f],(!l||l(p,i,o))&&!h[p]&&(o[p]=i[p],h[p]=!0);i=s!==!1&&su(i)}while(i&&(!s||s(i,o))&&i!==Object.prototype);return o},Zm=(i,o,s)=>{i=String(i),(s===void 0||s>i.length)&&(s=i.length),s-=o.length;const l=i.indexOf(o,s);return l!==-1&&l===s},ev=i=>{if(!i)return null;if(or(i))return i;let o=i.length;if(!ed(o))return null;const s=new Array(o);for(;o-- >0;)s[o]=i[o];return s},tv=(i=>o=>i&&o instanceof i)(typeof Uint8Array<"u"&&su(Uint8Array)),nv=(i,o)=>{const l=(i&&i[Io]).call(i);let a;for(;(a=l.next())&&!a.done;){const f=a.value;o.call(i,f[0],f[1])}},rv=(i,o)=>{let s;const l=[];for(;(s=i.exec(o))!==null;)l.push(s);return l},iv=Ot("HTMLFormElement"),ov=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,l,a){return l.toUpperCase()+a}),Ef=(({hasOwnProperty:i})=>(o,s)=>i.call(o,s))(Object.prototype),sv=Ot("RegExp"),rd=(i,o)=>{const s=Object.getOwnPropertyDescriptors(i),l={};Zr(s,(a,f)=>{let p;(p=o(a,f,i))!==!1&&(l[f]=p||a)}),Object.defineProperties(i,l)},lv=i=>{rd(i,(o,s)=>{if(st(i)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const l=i[s];if(st(l)){if(o.enumerable=!1,"writable"in o){o.writable=!1;return}o.set||(o.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},uv=(i,o)=>{const s={},l=a=>{a.forEach(f=>{s[f]=!0})};return or(i)?l(i):l(String(i).split(o)),s},av=()=>{},cv=(i,o)=>i!=null&&Number.isFinite(i=+i)?i:o;function fv(i){return!!(i&&st(i.append)&&i[Yf]==="FormData"&&i[Io])}const dv=i=>{const o=new Array(10),s=(l,a)=>{if(Mo(l)){if(o.indexOf(l)>=0)return;if(!("toJSON"in l)){o[a]=l;const f=or(l)?[]:{};return Zr(l,(p,h)=>{const y=s(p,a+1);!Jr(y)&&(f[h]=y)}),o[a]=void 0,f}}return l};return s(i,0)},pv=Ot("AsyncFunction"),hv=i=>i&&(Mo(i)||st(i))&&st(i.then)&&st(i.catch),id=((i,o)=>i?setImmediate:o?((s,l)=>(Fn.addEventListener("message",({source:a,data:f})=>{a===Fn&&f===s&&l.length&&l.shift()()},!1),a=>{l.push(a),Fn.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",st(Fn.postMessage)),mv=typeof queueMicrotask<"u"?queueMicrotask.bind(Fn):typeof process<"u"&&process.nextTick||id,vv=i=>i!=null&&st(i[Io]),P={isArray:or,isArrayBuffer:Zf,isBuffer:jm,isFormData:Bm,isArrayBufferView:Im,isString:Lm,isNumber:ed,isBoolean:Dm,isObject:Mo,isPlainObject:go,isReadableStream:bm,isRequest:Vm,isResponse:Hm,isHeaders:Km,isUndefined:Jr,isDate:Mm,isFile:Um,isBlob:zm,isRegExp:sv,isFunction:st,isStream:$m,isURLSearchParams:qm,isTypedArray:tv,isFileList:Qm,forEach:Zr,merge:Gl,extend:Gm,trim:Wm,stripBOM:Jm,inherits:Xm,toFlatObject:Ym,kindOf:Lo,kindOfTest:Ot,endsWith:Zm,toArray:ev,forEachEntry:nv,matchAll:rv,isHTMLForm:iv,hasOwnProperty:Ef,hasOwnProp:Ef,reduceDescriptors:rd,freezeMethods:lv,toObjectSet:uv,toCamelCase:ov,noop:av,toFiniteNumber:cv,findKey:td,global:Fn,isContextDefined:nd,isSpecCompliantForm:fv,toJSONObject:dv,isAsyncFn:pv,isThenable:hv,setImmediate:id,asap:mv,isIterable:vv};function oe(i,o,s,l,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",o&&(this.code=o),s&&(this.config=s),l&&(this.request=l),a&&(this.response=a,this.status=a.status?a.status:null)}P.inherits(oe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:P.toJSONObject(this.config),code:this.code,status:this.status}}});const od=oe.prototype,sd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{sd[i]={value:i}});Object.defineProperties(oe,sd);Object.defineProperty(od,"isAxiosError",{value:!0});oe.from=(i,o,s,l,a,f)=>{const p=Object.create(od);return P.toFlatObject(i,p,function(y){return y!==Error.prototype},h=>h!=="isAxiosError"),oe.call(p,i.message,o,s,l,a),p.cause=i,p.name=i.name,f&&Object.assign(p,f),p};const yv=null;function Jl(i){return P.isPlainObject(i)||P.isArray(i)}function ld(i){return P.endsWith(i,"[]")?i.slice(0,-2):i}function kf(i,o,s){return i?i.concat(o).map(function(a,f){return a=ld(a),!s&&f?"["+a+"]":a}).join(s?".":""):o}function gv(i){return P.isArray(i)&&!i.some(Jl)}const wv=P.toFlatObject(P,{},null,function(o){return/^is[A-Z]/.test(o)});function Uo(i,o,s){if(!P.isObject(i))throw new TypeError("target must be an object");o=o||new FormData,s=P.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(A,N){return!P.isUndefined(N[A])});const l=s.metaTokens,a=s.visitor||w,f=s.dots,p=s.indexes,y=(s.Blob||typeof Blob<"u"&&Blob)&&P.isSpecCompliantForm(o);if(!P.isFunction(a))throw new TypeError("visitor must be a function");function v(T){if(T===null)return"";if(P.isDate(T))return T.toISOString();if(!y&&P.isBlob(T))throw new oe("Blob is not supported. Use a Buffer instead.");return P.isArrayBuffer(T)||P.isTypedArray(T)?y&&typeof Blob=="function"?new Blob([T]):Buffer.from(T):T}function w(T,A,N){let $=T;if(T&&!N&&typeof T=="object"){if(P.endsWith(A,"{}"))A=l?A:A.slice(0,-2),T=JSON.stringify(T);else if(P.isArray(T)&&gv(T)||(P.isFileList(T)||P.endsWith(A,"[]"))&&($=P.toArray(T)))return A=ld(A),$.forEach(function(z,b){!(P.isUndefined(z)||z===null)&&o.append(p===!0?kf([A],b,f):p===null?A:A+"[]",v(z))}),!1}return Jl(T)?!0:(o.append(kf(N,A,f),v(T)),!1)}const E=[],F=Object.assign(wv,{defaultVisitor:w,convertValue:v,isVisitable:Jl});function j(T,A){if(!P.isUndefined(T)){if(E.indexOf(T)!==-1)throw Error("Circular reference detected in "+A.join("."));E.push(T),P.forEach(T,function($,q){(!(P.isUndefined($)||$===null)&&a.call(o,$,P.isString(q)?q.trim():q,A,F))===!0&&j($,A?A.concat(q):[q])}),E.pop()}}if(!P.isObject(i))throw new TypeError("data must be an object");return j(i),o}function Rf(i){const o={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(l){return o[l]})}function lu(i,o){this._pairs=[],i&&Uo(i,this,o)}const ud=lu.prototype;ud.append=function(o,s){this._pairs.push([o,s])};ud.toString=function(o){const s=o?function(l){return o.call(this,l,Rf)}:Rf;return this._pairs.map(function(a){return s(a[0])+"="+s(a[1])},"").join("&")};function Sv(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ad(i,o,s){if(!o)return i;const l=s&&s.encode||Sv;P.isFunction(s)&&(s={serialize:s});const a=s&&s.serialize;let f;if(a?f=a(o,s):f=P.isURLSearchParams(o)?o.toString():new lu(o,s).toString(l),f){const p=i.indexOf("#");p!==-1&&(i=i.slice(0,p)),i+=(i.indexOf("?")===-1?"?":"&")+f}return i}class _f{constructor(){this.handlers=[]}use(o,s,l){return this.handlers.push({fulfilled:o,rejected:s,synchronous:l?l.synchronous:!1,runWhen:l?l.runWhen:null}),this.handlers.length-1}eject(o){this.handlers[o]&&(this.handlers[o]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(o){P.forEach(this.handlers,function(l){l!==null&&o(l)})}}const cd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},xv=typeof URLSearchParams<"u"?URLSearchParams:lu,Cv=typeof FormData<"u"?FormData:null,Ev=typeof Blob<"u"?Blob:null,kv={isBrowser:!0,classes:{URLSearchParams:xv,FormData:Cv,Blob:Ev},protocols:["http","https","file","blob","url","data"]},uu=typeof window<"u"&&typeof document<"u",Xl=typeof navigator=="object"&&navigator||void 0,Rv=uu&&(!Xl||["ReactNative","NativeScript","NS"].indexOf(Xl.product)<0),_v=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ov=uu&&window.location.href||"http://localhost",Pv=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:uu,hasStandardBrowserEnv:Rv,hasStandardBrowserWebWorkerEnv:_v,navigator:Xl,origin:Ov},Symbol.toStringTag,{value:"Module"})),Xe={...Pv,...kv};function Nv(i,o){return Uo(i,new Xe.classes.URLSearchParams,Object.assign({visitor:function(s,l,a,f){return Xe.isNode&&P.isBuffer(s)?(this.append(l,s.toString("base64")),!1):f.defaultVisitor.apply(this,arguments)}},o))}function Tv(i){return P.matchAll(/\w+|\[(\w*)]/g,i).map(o=>o[0]==="[]"?"":o[1]||o[0])}function Fv(i){const o={},s=Object.keys(i);let l;const a=s.length;let f;for(l=0;l<a;l++)f=s[l],o[f]=i[f];return o}function fd(i){function o(s,l,a,f){let p=s[f++];if(p==="__proto__")return!0;const h=Number.isFinite(+p),y=f>=s.length;return p=!p&&P.isArray(a)?a.length:p,y?(P.hasOwnProp(a,p)?a[p]=[a[p],l]:a[p]=l,!h):((!a[p]||!P.isObject(a[p]))&&(a[p]=[]),o(s,l,a[p],f)&&P.isArray(a[p])&&(a[p]=Fv(a[p])),!h)}if(P.isFormData(i)&&P.isFunction(i.entries)){const s={};return P.forEachEntry(i,(l,a)=>{o(Tv(l),a,s,0)}),s}return null}function Av(i,o,s){if(P.isString(i))try{return(o||JSON.parse)(i),P.trim(i)}catch(l){if(l.name!=="SyntaxError")throw l}return(s||JSON.stringify)(i)}const ei={transitional:cd,adapter:["xhr","http","fetch"],transformRequest:[function(o,s){const l=s.getContentType()||"",a=l.indexOf("application/json")>-1,f=P.isObject(o);if(f&&P.isHTMLForm(o)&&(o=new FormData(o)),P.isFormData(o))return a?JSON.stringify(fd(o)):o;if(P.isArrayBuffer(o)||P.isBuffer(o)||P.isStream(o)||P.isFile(o)||P.isBlob(o)||P.isReadableStream(o))return o;if(P.isArrayBufferView(o))return o.buffer;if(P.isURLSearchParams(o))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),o.toString();let h;if(f){if(l.indexOf("application/x-www-form-urlencoded")>-1)return Nv(o,this.formSerializer).toString();if((h=P.isFileList(o))||l.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return Uo(h?{"files[]":o}:o,y&&new y,this.formSerializer)}}return f||a?(s.setContentType("application/json",!1),Av(o)):o}],transformResponse:[function(o){const s=this.transitional||ei.transitional,l=s&&s.forcedJSONParsing,a=this.responseType==="json";if(P.isResponse(o)||P.isReadableStream(o))return o;if(o&&P.isString(o)&&(l&&!this.responseType||a)){const p=!(s&&s.silentJSONParsing)&&a;try{return JSON.parse(o)}catch(h){if(p)throw h.name==="SyntaxError"?oe.from(h,oe.ERR_BAD_RESPONSE,this,null,this.response):h}}return o}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Xe.classes.FormData,Blob:Xe.classes.Blob},validateStatus:function(o){return o>=200&&o<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};P.forEach(["delete","get","head","post","put","patch"],i=>{ei.headers[i]={}});const jv=P.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Iv=i=>{const o={};let s,l,a;return i&&i.split(`
`).forEach(function(p){a=p.indexOf(":"),s=p.substring(0,a).trim().toLowerCase(),l=p.substring(a+1).trim(),!(!s||o[s]&&jv[s])&&(s==="set-cookie"?o[s]?o[s].push(l):o[s]=[l]:o[s]=o[s]?o[s]+", "+l:l)}),o},Of=Symbol("internals");function Wr(i){return i&&String(i).trim().toLowerCase()}function wo(i){return i===!1||i==null?i:P.isArray(i)?i.map(wo):String(i)}function Lv(i){const o=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let l;for(;l=s.exec(i);)o[l[1]]=l[2];return o}const Dv=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function $l(i,o,s,l,a){if(P.isFunction(l))return l.call(this,o,s);if(a&&(o=s),!!P.isString(o)){if(P.isString(l))return o.indexOf(l)!==-1;if(P.isRegExp(l))return l.test(o)}}function Mv(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(o,s,l)=>s.toUpperCase()+l)}function Uv(i,o){const s=P.toCamelCase(" "+o);["get","set","has"].forEach(l=>{Object.defineProperty(i,l+s,{value:function(a,f,p){return this[l].call(this,o,a,f,p)},configurable:!0})})}let lt=class{constructor(o){o&&this.set(o)}set(o,s,l){const a=this;function f(h,y,v){const w=Wr(y);if(!w)throw new Error("header name must be a non-empty string");const E=P.findKey(a,w);(!E||a[E]===void 0||v===!0||v===void 0&&a[E]!==!1)&&(a[E||y]=wo(h))}const p=(h,y)=>P.forEach(h,(v,w)=>f(v,w,y));if(P.isPlainObject(o)||o instanceof this.constructor)p(o,s);else if(P.isString(o)&&(o=o.trim())&&!Dv(o))p(Iv(o),s);else if(P.isObject(o)&&P.isIterable(o)){let h={},y,v;for(const w of o){if(!P.isArray(w))throw TypeError("Object iterator must return a key-value pair");h[v=w[0]]=(y=h[v])?P.isArray(y)?[...y,w[1]]:[y,w[1]]:w[1]}p(h,s)}else o!=null&&f(s,o,l);return this}get(o,s){if(o=Wr(o),o){const l=P.findKey(this,o);if(l){const a=this[l];if(!s)return a;if(s===!0)return Lv(a);if(P.isFunction(s))return s.call(this,a,l);if(P.isRegExp(s))return s.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(o,s){if(o=Wr(o),o){const l=P.findKey(this,o);return!!(l&&this[l]!==void 0&&(!s||$l(this,this[l],l,s)))}return!1}delete(o,s){const l=this;let a=!1;function f(p){if(p=Wr(p),p){const h=P.findKey(l,p);h&&(!s||$l(l,l[h],h,s))&&(delete l[h],a=!0)}}return P.isArray(o)?o.forEach(f):f(o),a}clear(o){const s=Object.keys(this);let l=s.length,a=!1;for(;l--;){const f=s[l];(!o||$l(this,this[f],f,o,!0))&&(delete this[f],a=!0)}return a}normalize(o){const s=this,l={};return P.forEach(this,(a,f)=>{const p=P.findKey(l,f);if(p){s[p]=wo(a),delete s[f];return}const h=o?Mv(f):String(f).trim();h!==f&&delete s[f],s[h]=wo(a),l[h]=!0}),this}concat(...o){return this.constructor.concat(this,...o)}toJSON(o){const s=Object.create(null);return P.forEach(this,(l,a)=>{l!=null&&l!==!1&&(s[a]=o&&P.isArray(l)?l.join(", "):l)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([o,s])=>o+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(o){return o instanceof this?o:new this(o)}static concat(o,...s){const l=new this(o);return s.forEach(a=>l.set(a)),l}static accessor(o){const l=(this[Of]=this[Of]={accessors:{}}).accessors,a=this.prototype;function f(p){const h=Wr(p);l[h]||(Uv(a,p),l[h]=!0)}return P.isArray(o)?o.forEach(f):f(o),this}};lt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);P.reduceDescriptors(lt.prototype,({value:i},o)=>{let s=o[0].toUpperCase()+o.slice(1);return{get:()=>i,set(l){this[s]=l}}});P.freezeMethods(lt);function Bl(i,o){const s=this||ei,l=o||s,a=lt.from(l.headers);let f=l.data;return P.forEach(i,function(h){f=h.call(s,f,a.normalize(),o?o.status:void 0)}),a.normalize(),f}function dd(i){return!!(i&&i.__CANCEL__)}function sr(i,o,s){oe.call(this,i??"canceled",oe.ERR_CANCELED,o,s),this.name="CanceledError"}P.inherits(sr,oe,{__CANCEL__:!0});function pd(i,o,s){const l=s.config.validateStatus;!s.status||!l||l(s.status)?i(s):o(new oe("Request failed with status code "+s.status,[oe.ERR_BAD_REQUEST,oe.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function zv(i){const o=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return o&&o[1]||""}function Qv(i,o){i=i||10;const s=new Array(i),l=new Array(i);let a=0,f=0,p;return o=o!==void 0?o:1e3,function(y){const v=Date.now(),w=l[f];p||(p=v),s[a]=y,l[a]=v;let E=f,F=0;for(;E!==a;)F+=s[E++],E=E%i;if(a=(a+1)%i,a===f&&(f=(f+1)%i),v-p<o)return;const j=w&&v-w;return j?Math.round(F*1e3/j):void 0}}function $v(i,o){let s=0,l=1e3/o,a,f;const p=(v,w=Date.now())=>{s=w,a=null,f&&(clearTimeout(f),f=null),i.apply(null,v)};return[(...v)=>{const w=Date.now(),E=w-s;E>=l?p(v,w):(a=v,f||(f=setTimeout(()=>{f=null,p(a)},l-E)))},()=>a&&p(a)]}const No=(i,o,s=3)=>{let l=0;const a=Qv(50,250);return $v(f=>{const p=f.loaded,h=f.lengthComputable?f.total:void 0,y=p-l,v=a(y),w=p<=h;l=p;const E={loaded:p,total:h,progress:h?p/h:void 0,bytes:y,rate:v||void 0,estimated:v&&h&&w?(h-p)/v:void 0,event:f,lengthComputable:h!=null,[o?"download":"upload"]:!0};i(E)},s)},Pf=(i,o)=>{const s=i!=null;return[l=>o[0]({lengthComputable:s,total:i,loaded:l}),o[1]]},Nf=i=>(...o)=>P.asap(()=>i(...o)),Bv=Xe.hasStandardBrowserEnv?((i,o)=>s=>(s=new URL(s,Xe.origin),i.protocol===s.protocol&&i.host===s.host&&(o||i.port===s.port)))(new URL(Xe.origin),Xe.navigator&&/(msie|trident)/i.test(Xe.navigator.userAgent)):()=>!0,qv=Xe.hasStandardBrowserEnv?{write(i,o,s,l,a,f){const p=[i+"="+encodeURIComponent(o)];P.isNumber(s)&&p.push("expires="+new Date(s).toGMTString()),P.isString(l)&&p.push("path="+l),P.isString(a)&&p.push("domain="+a),f===!0&&p.push("secure"),document.cookie=p.join("; ")},read(i){const o=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return o?decodeURIComponent(o[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function bv(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function Vv(i,o){return o?i.replace(/\/?\/$/,"")+"/"+o.replace(/^\/+/,""):i}function hd(i,o,s){let l=!bv(o);return i&&(l||s==!1)?Vv(i,o):o}const Tf=i=>i instanceof lt?{...i}:i;function jn(i,o){o=o||{};const s={};function l(v,w,E,F){return P.isPlainObject(v)&&P.isPlainObject(w)?P.merge.call({caseless:F},v,w):P.isPlainObject(w)?P.merge({},w):P.isArray(w)?w.slice():w}function a(v,w,E,F){if(P.isUndefined(w)){if(!P.isUndefined(v))return l(void 0,v,E,F)}else return l(v,w,E,F)}function f(v,w){if(!P.isUndefined(w))return l(void 0,w)}function p(v,w){if(P.isUndefined(w)){if(!P.isUndefined(v))return l(void 0,v)}else return l(void 0,w)}function h(v,w,E){if(E in o)return l(v,w);if(E in i)return l(void 0,v)}const y={url:f,method:f,data:f,baseURL:p,transformRequest:p,transformResponse:p,paramsSerializer:p,timeout:p,timeoutMessage:p,withCredentials:p,withXSRFToken:p,adapter:p,responseType:p,xsrfCookieName:p,xsrfHeaderName:p,onUploadProgress:p,onDownloadProgress:p,decompress:p,maxContentLength:p,maxBodyLength:p,beforeRedirect:p,transport:p,httpAgent:p,httpsAgent:p,cancelToken:p,socketPath:p,responseEncoding:p,validateStatus:h,headers:(v,w,E)=>a(Tf(v),Tf(w),E,!0)};return P.forEach(Object.keys(Object.assign({},i,o)),function(w){const E=y[w]||a,F=E(i[w],o[w],w);P.isUndefined(F)&&E!==h||(s[w]=F)}),s}const md=i=>{const o=jn({},i);let{data:s,withXSRFToken:l,xsrfHeaderName:a,xsrfCookieName:f,headers:p,auth:h}=o;o.headers=p=lt.from(p),o.url=ad(hd(o.baseURL,o.url,o.allowAbsoluteUrls),i.params,i.paramsSerializer),h&&p.set("Authorization","Basic "+btoa((h.username||"")+":"+(h.password?unescape(encodeURIComponent(h.password)):"")));let y;if(P.isFormData(s)){if(Xe.hasStandardBrowserEnv||Xe.hasStandardBrowserWebWorkerEnv)p.setContentType(void 0);else if((y=p.getContentType())!==!1){const[v,...w]=y?y.split(";").map(E=>E.trim()).filter(Boolean):[];p.setContentType([v||"multipart/form-data",...w].join("; "))}}if(Xe.hasStandardBrowserEnv&&(l&&P.isFunction(l)&&(l=l(o)),l||l!==!1&&Bv(o.url))){const v=a&&f&&qv.read(f);v&&p.set(a,v)}return o},Hv=typeof XMLHttpRequest<"u",Kv=Hv&&function(i){return new Promise(function(s,l){const a=md(i);let f=a.data;const p=lt.from(a.headers).normalize();let{responseType:h,onUploadProgress:y,onDownloadProgress:v}=a,w,E,F,j,T;function A(){j&&j(),T&&T(),a.cancelToken&&a.cancelToken.unsubscribe(w),a.signal&&a.signal.removeEventListener("abort",w)}let N=new XMLHttpRequest;N.open(a.method.toUpperCase(),a.url,!0),N.timeout=a.timeout;function $(){if(!N)return;const z=lt.from("getAllResponseHeaders"in N&&N.getAllResponseHeaders()),ie={data:!h||h==="text"||h==="json"?N.responseText:N.response,status:N.status,statusText:N.statusText,headers:z,config:i,request:N};pd(function(ue){s(ue),A()},function(ue){l(ue),A()},ie),N=null}"onloadend"in N?N.onloadend=$:N.onreadystatechange=function(){!N||N.readyState!==4||N.status===0&&!(N.responseURL&&N.responseURL.indexOf("file:")===0)||setTimeout($)},N.onabort=function(){N&&(l(new oe("Request aborted",oe.ECONNABORTED,i,N)),N=null)},N.onerror=function(){l(new oe("Network Error",oe.ERR_NETWORK,i,N)),N=null},N.ontimeout=function(){let b=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const ie=a.transitional||cd;a.timeoutErrorMessage&&(b=a.timeoutErrorMessage),l(new oe(b,ie.clarifyTimeoutError?oe.ETIMEDOUT:oe.ECONNABORTED,i,N)),N=null},f===void 0&&p.setContentType(null),"setRequestHeader"in N&&P.forEach(p.toJSON(),function(b,ie){N.setRequestHeader(ie,b)}),P.isUndefined(a.withCredentials)||(N.withCredentials=!!a.withCredentials),h&&h!=="json"&&(N.responseType=a.responseType),v&&([F,T]=No(v,!0),N.addEventListener("progress",F)),y&&N.upload&&([E,j]=No(y),N.upload.addEventListener("progress",E),N.upload.addEventListener("loadend",j)),(a.cancelToken||a.signal)&&(w=z=>{N&&(l(!z||z.type?new sr(null,i,N):z),N.abort(),N=null)},a.cancelToken&&a.cancelToken.subscribe(w),a.signal&&(a.signal.aborted?w():a.signal.addEventListener("abort",w)));const q=zv(a.url);if(q&&Xe.protocols.indexOf(q)===-1){l(new oe("Unsupported protocol "+q+":",oe.ERR_BAD_REQUEST,i));return}N.send(f||null)})},Wv=(i,o)=>{const{length:s}=i=i?i.filter(Boolean):[];if(o||s){let l=new AbortController,a;const f=function(v){if(!a){a=!0,h();const w=v instanceof Error?v:this.reason;l.abort(w instanceof oe?w:new sr(w instanceof Error?w.message:w))}};let p=o&&setTimeout(()=>{p=null,f(new oe(`timeout ${o} of ms exceeded`,oe.ETIMEDOUT))},o);const h=()=>{i&&(p&&clearTimeout(p),p=null,i.forEach(v=>{v.unsubscribe?v.unsubscribe(f):v.removeEventListener("abort",f)}),i=null)};i.forEach(v=>v.addEventListener("abort",f));const{signal:y}=l;return y.unsubscribe=()=>P.asap(h),y}},Gv=function*(i,o){let s=i.byteLength;if(s<o){yield i;return}let l=0,a;for(;l<s;)a=l+o,yield i.slice(l,a),l=a},Jv=async function*(i,o){for await(const s of Xv(i))yield*Gv(s,o)},Xv=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const o=i.getReader();try{for(;;){const{done:s,value:l}=await o.read();if(s)break;yield l}}finally{await o.cancel()}},Ff=(i,o,s,l)=>{const a=Jv(i,o);let f=0,p,h=y=>{p||(p=!0,l&&l(y))};return new ReadableStream({async pull(y){try{const{done:v,value:w}=await a.next();if(v){h(),y.close();return}let E=w.byteLength;if(s){let F=f+=E;s(F)}y.enqueue(new Uint8Array(w))}catch(v){throw h(v),v}},cancel(y){return h(y),a.return()}},{highWaterMark:2})},zo=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",vd=zo&&typeof ReadableStream=="function",Yv=zo&&(typeof TextEncoder=="function"?(i=>o=>i.encode(o))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),yd=(i,...o)=>{try{return!!i(...o)}catch{return!1}},Zv=vd&&yd(()=>{let i=!1;const o=new Request(Xe.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!o}),Af=64*1024,Yl=vd&&yd(()=>P.isReadableStream(new Response("").body)),To={stream:Yl&&(i=>i.body)};zo&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(o=>{!To[o]&&(To[o]=P.isFunction(i[o])?s=>s[o]():(s,l)=>{throw new oe(`Response type '${o}' is not supported`,oe.ERR_NOT_SUPPORT,l)})})})(new Response);const ey=async i=>{if(i==null)return 0;if(P.isBlob(i))return i.size;if(P.isSpecCompliantForm(i))return(await new Request(Xe.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(P.isArrayBufferView(i)||P.isArrayBuffer(i))return i.byteLength;if(P.isURLSearchParams(i)&&(i=i+""),P.isString(i))return(await Yv(i)).byteLength},ty=async(i,o)=>{const s=P.toFiniteNumber(i.getContentLength());return s??ey(o)},ny=zo&&(async i=>{let{url:o,method:s,data:l,signal:a,cancelToken:f,timeout:p,onDownloadProgress:h,onUploadProgress:y,responseType:v,headers:w,withCredentials:E="same-origin",fetchOptions:F}=md(i);v=v?(v+"").toLowerCase():"text";let j=Wv([a,f&&f.toAbortSignal()],p),T;const A=j&&j.unsubscribe&&(()=>{j.unsubscribe()});let N;try{if(y&&Zv&&s!=="get"&&s!=="head"&&(N=await ty(w,l))!==0){let ie=new Request(o,{method:"POST",body:l,duplex:"half"}),Z;if(P.isFormData(l)&&(Z=ie.headers.get("content-type"))&&w.setContentType(Z),ie.body){const[ue,le]=Pf(N,No(Nf(y)));l=Ff(ie.body,Af,ue,le)}}P.isString(E)||(E=E?"include":"omit");const $="credentials"in Request.prototype;T=new Request(o,{...F,signal:j,method:s.toUpperCase(),headers:w.normalize().toJSON(),body:l,duplex:"half",credentials:$?E:void 0});let q=await fetch(T);const z=Yl&&(v==="stream"||v==="response");if(Yl&&(h||z&&A)){const ie={};["status","statusText","headers"].forEach(De=>{ie[De]=q[De]});const Z=P.toFiniteNumber(q.headers.get("content-length")),[ue,le]=h&&Pf(Z,No(Nf(h),!0))||[];q=new Response(Ff(q.body,Af,ue,()=>{le&&le(),A&&A()}),ie)}v=v||"text";let b=await To[P.findKey(To,v)||"text"](q,i);return!z&&A&&A(),await new Promise((ie,Z)=>{pd(ie,Z,{data:b,headers:lt.from(q.headers),status:q.status,statusText:q.statusText,config:i,request:T})})}catch($){throw A&&A(),$&&$.name==="TypeError"&&/Load failed|fetch/i.test($.message)?Object.assign(new oe("Network Error",oe.ERR_NETWORK,i,T),{cause:$.cause||$}):oe.from($,$&&$.code,i,T)}}),Zl={http:yv,xhr:Kv,fetch:ny};P.forEach(Zl,(i,o)=>{if(i){try{Object.defineProperty(i,"name",{value:o})}catch{}Object.defineProperty(i,"adapterName",{value:o})}});const jf=i=>`- ${i}`,ry=i=>P.isFunction(i)||i===null||i===!1,gd={getAdapter:i=>{i=P.isArray(i)?i:[i];const{length:o}=i;let s,l;const a={};for(let f=0;f<o;f++){s=i[f];let p;if(l=s,!ry(s)&&(l=Zl[(p=String(s)).toLowerCase()],l===void 0))throw new oe(`Unknown adapter '${p}'`);if(l)break;a[p||"#"+f]=l}if(!l){const f=Object.entries(a).map(([h,y])=>`adapter ${h} `+(y===!1?"is not supported by the environment":"is not available in the build"));let p=o?f.length>1?`since :
`+f.map(jf).join(`
`):" "+jf(f[0]):"as no adapter specified";throw new oe("There is no suitable adapter to dispatch the request "+p,"ERR_NOT_SUPPORT")}return l},adapters:Zl};function ql(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new sr(null,i)}function If(i){return ql(i),i.headers=lt.from(i.headers),i.data=Bl.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),gd.getAdapter(i.adapter||ei.adapter)(i).then(function(l){return ql(i),l.data=Bl.call(i,i.transformResponse,l),l.headers=lt.from(l.headers),l},function(l){return dd(l)||(ql(i),l&&l.response&&(l.response.data=Bl.call(i,i.transformResponse,l.response),l.response.headers=lt.from(l.response.headers))),Promise.reject(l)})}const wd="1.9.0",Qo={};["object","boolean","number","function","string","symbol"].forEach((i,o)=>{Qo[i]=function(l){return typeof l===i||"a"+(o<1?"n ":" ")+i}});const Lf={};Qo.transitional=function(o,s,l){function a(f,p){return"[Axios v"+wd+"] Transitional option '"+f+"'"+p+(l?". "+l:"")}return(f,p,h)=>{if(o===!1)throw new oe(a(p," has been removed"+(s?" in "+s:"")),oe.ERR_DEPRECATED);return s&&!Lf[p]&&(Lf[p]=!0,console.warn(a(p," has been deprecated since v"+s+" and will be removed in the near future"))),o?o(f,p,h):!0}};Qo.spelling=function(o){return(s,l)=>(console.warn(`${l} is likely a misspelling of ${o}`),!0)};function iy(i,o,s){if(typeof i!="object")throw new oe("options must be an object",oe.ERR_BAD_OPTION_VALUE);const l=Object.keys(i);let a=l.length;for(;a-- >0;){const f=l[a],p=o[f];if(p){const h=i[f],y=h===void 0||p(h,f,i);if(y!==!0)throw new oe("option "+f+" must be "+y,oe.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new oe("Unknown option "+f,oe.ERR_BAD_OPTION)}}const So={assertOptions:iy,validators:Qo},jt=So.validators;let An=class{constructor(o){this.defaults=o||{},this.interceptors={request:new _f,response:new _f}}async request(o,s){try{return await this._request(o,s)}catch(l){if(l instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const f=a.stack?a.stack.replace(/^.+\n/,""):"";try{l.stack?f&&!String(l.stack).endsWith(f.replace(/^.+\n.+\n/,""))&&(l.stack+=`
`+f):l.stack=f}catch{}}throw l}}_request(o,s){typeof o=="string"?(s=s||{},s.url=o):s=o||{},s=jn(this.defaults,s);const{transitional:l,paramsSerializer:a,headers:f}=s;l!==void 0&&So.assertOptions(l,{silentJSONParsing:jt.transitional(jt.boolean),forcedJSONParsing:jt.transitional(jt.boolean),clarifyTimeoutError:jt.transitional(jt.boolean)},!1),a!=null&&(P.isFunction(a)?s.paramsSerializer={serialize:a}:So.assertOptions(a,{encode:jt.function,serialize:jt.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),So.assertOptions(s,{baseUrl:jt.spelling("baseURL"),withXsrfToken:jt.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let p=f&&P.merge(f.common,f[s.method]);f&&P.forEach(["delete","get","head","post","put","patch","common"],T=>{delete f[T]}),s.headers=lt.concat(p,f);const h=[];let y=!0;this.interceptors.request.forEach(function(A){typeof A.runWhen=="function"&&A.runWhen(s)===!1||(y=y&&A.synchronous,h.unshift(A.fulfilled,A.rejected))});const v=[];this.interceptors.response.forEach(function(A){v.push(A.fulfilled,A.rejected)});let w,E=0,F;if(!y){const T=[If.bind(this),void 0];for(T.unshift.apply(T,h),T.push.apply(T,v),F=T.length,w=Promise.resolve(s);E<F;)w=w.then(T[E++],T[E++]);return w}F=h.length;let j=s;for(E=0;E<F;){const T=h[E++],A=h[E++];try{j=T(j)}catch(N){A.call(this,N);break}}try{w=If.call(this,j)}catch(T){return Promise.reject(T)}for(E=0,F=v.length;E<F;)w=w.then(v[E++],v[E++]);return w}getUri(o){o=jn(this.defaults,o);const s=hd(o.baseURL,o.url,o.allowAbsoluteUrls);return ad(s,o.params,o.paramsSerializer)}};P.forEach(["delete","get","head","options"],function(o){An.prototype[o]=function(s,l){return this.request(jn(l||{},{method:o,url:s,data:(l||{}).data}))}});P.forEach(["post","put","patch"],function(o){function s(l){return function(f,p,h){return this.request(jn(h||{},{method:o,headers:l?{"Content-Type":"multipart/form-data"}:{},url:f,data:p}))}}An.prototype[o]=s(),An.prototype[o+"Form"]=s(!0)});let oy=class Sd{constructor(o){if(typeof o!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(f){s=f});const l=this;this.promise.then(a=>{if(!l._listeners)return;let f=l._listeners.length;for(;f-- >0;)l._listeners[f](a);l._listeners=null}),this.promise.then=a=>{let f;const p=new Promise(h=>{l.subscribe(h),f=h}).then(a);return p.cancel=function(){l.unsubscribe(f)},p},o(function(f,p,h){l.reason||(l.reason=new sr(f,p,h),s(l.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(o){if(this.reason){o(this.reason);return}this._listeners?this._listeners.push(o):this._listeners=[o]}unsubscribe(o){if(!this._listeners)return;const s=this._listeners.indexOf(o);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const o=new AbortController,s=l=>{o.abort(l)};return this.subscribe(s),o.signal.unsubscribe=()=>this.unsubscribe(s),o.signal}static source(){let o;return{token:new Sd(function(a){o=a}),cancel:o}}};function sy(i){return function(s){return i.apply(null,s)}}function ly(i){return P.isObject(i)&&i.isAxiosError===!0}const eu={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(eu).forEach(([i,o])=>{eu[o]=i});function xd(i){const o=new An(i),s=Xf(An.prototype.request,o);return P.extend(s,An.prototype,o,{allOwnKeys:!0}),P.extend(s,o,null,{allOwnKeys:!0}),s.create=function(a){return xd(jn(i,a))},s}const Re=xd(ei);Re.Axios=An;Re.CanceledError=sr;Re.CancelToken=oy;Re.isCancel=dd;Re.VERSION=wd;Re.toFormData=Uo;Re.AxiosError=oe;Re.Cancel=Re.CanceledError;Re.all=function(o){return Promise.all(o)};Re.spread=sy;Re.isAxiosError=ly;Re.mergeConfig=jn;Re.AxiosHeaders=lt;Re.formToJSON=i=>fd(P.isHTMLForm(i)?new FormData(i):i);Re.getAdapter=gd.getAdapter;Re.HttpStatusCode=eu;Re.default=Re;const{Axios:eg,AxiosError:tg,CanceledError:ng,isCancel:rg,CancelToken:ig,VERSION:og,all:sg,Cancel:lg,isAxiosError:ug,spread:ag,toFormData:cg,AxiosHeaders:fg,HttpStatusCode:dg,formToJSON:pg,getAdapter:hg,mergeConfig:mg}=Re;function Cd(i){var o,s,l="";if(typeof i=="string"||typeof i=="number")l+=i;else if(typeof i=="object")if(Array.isArray(i)){var a=i.length;for(o=0;o<a;o++)i[o]&&(s=Cd(i[o]))&&(l&&(l+=" "),l+=s)}else for(s in i)i[s]&&(l&&(l+=" "),l+=s);return l}function uy(){for(var i,o,s=0,l="",a=arguments.length;s<a;s++)(i=arguments[s])&&(o=Cd(i))&&(l&&(l+=" "),l+=o);return l}const Df=i=>typeof i=="boolean"?`${i}`:i===0?"0":i,Mf=uy,ay=(i,o)=>s=>{var l;if((o==null?void 0:o.variants)==null)return Mf(i,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:a,defaultVariants:f}=o,p=Object.keys(a).map(v=>{const w=s==null?void 0:s[v],E=f==null?void 0:f[v];if(w===null)return null;const F=Df(w)||Df(E);return a[v][F]}),h=s&&Object.entries(s).reduce((v,w)=>{let[E,F]=w;return F===void 0||(v[E]=F),v},{}),y=o==null||(l=o.compoundVariants)===null||l===void 0?void 0:l.reduce((v,w)=>{let{class:E,className:F,...j}=w;return Object.entries(j).every(T=>{let[A,N]=T;return Array.isArray(N)?N.includes({...f,...h}[A]):{...f,...h}[A]===N})?[...v,E,F]:v},[]);return Mf(i,p,y,s==null?void 0:s.class,s==null?void 0:s.className)};function It(...i){return i.filter(Boolean).join(" ")}const cy=ay("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),_t=U.forwardRef(({className:i,variant:o,size:s,asChild:l=!1,...a},f)=>_.jsx("button",{className:It(cy({variant:o,size:s,className:i})),ref:f,...a}));_t.displayName="Button";const cn=U.forwardRef(({className:i,...o},s)=>_.jsx("div",{ref:s,className:It("rounded-lg border bg-card text-card-foreground shadow-sm",i),...o}));cn.displayName="Card";const fn=U.forwardRef(({className:i,...o},s)=>_.jsx("div",{ref:s,className:It("flex flex-col space-y-1.5 p-6",i),...o}));fn.displayName="CardHeader";const dn=U.forwardRef(({className:i,...o},s)=>_.jsx("h3",{ref:s,className:It("text-2xl font-semibold leading-none tracking-tight",i),...o}));dn.displayName="CardTitle";const Pn=U.forwardRef(({className:i,...o},s)=>_.jsx("p",{ref:s,className:It("text-sm text-muted-foreground",i),...o}));Pn.displayName="CardDescription";const pn=U.forwardRef(({className:i,...o},s)=>_.jsx("div",{ref:s,className:It("p-6 pt-0",i),...o}));pn.displayName="CardContent";const Nn=U.forwardRef(({className:i,...o},s)=>_.jsx("div",{ref:s,className:It("flex items-center p-6 pt-0",i),...o}));Nn.displayName="CardFooter";function qt(i,o,{checkForDefaultPrevented:s=!0}={}){return function(a){if(i==null||i(a),s===!1||!a.defaultPrevented)return o==null?void 0:o(a)}}function au(i,o=[]){let s=[];function l(f,p){const h=U.createContext(p),y=s.length;s=[...s,p];const v=E=>{var $;const{scope:F,children:j,...T}=E,A=(($=F==null?void 0:F[i])==null?void 0:$[y])||h,N=U.useMemo(()=>T,Object.values(T));return _.jsx(A.Provider,{value:N,children:j})};v.displayName=f+"Provider";function w(E,F){var A;const j=((A=F==null?void 0:F[i])==null?void 0:A[y])||h,T=U.useContext(j);if(T)return T;if(p!==void 0)return p;throw new Error(`\`${E}\` must be used within \`${f}\``)}return[v,w]}const a=()=>{const f=s.map(p=>U.createContext(p));return function(h){const y=(h==null?void 0:h[i])||f;return U.useMemo(()=>({[`__scope${i}`]:{...h,[i]:y}}),[h,y])}};return a.scopeName=i,[l,fy(a,...o)]}function fy(...i){const o=i[0];if(i.length===1)return o;const s=()=>{const l=i.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(f){const p=l.reduce((h,{useScope:y,scopeName:v})=>{const E=y(f)[`__scope${v}`];return{...h,...E}},{});return U.useMemo(()=>({[`__scope${o.scopeName}`]:p}),[p])}};return s.scopeName=o.scopeName,s}function Uf(i,o){if(typeof i=="function")return i(o);i!=null&&(i.current=o)}function Ed(...i){return o=>{let s=!1;const l=i.map(a=>{const f=Uf(a,o);return!s&&typeof f=="function"&&(s=!0),f});if(s)return()=>{for(let a=0;a<l.length;a++){const f=l[a];typeof f=="function"?f():Uf(i[a],null)}}}}function Fo(...i){return U.useCallback(Ed(...i),i)}function tu(i){const o=dy(i),s=U.forwardRef((l,a)=>{const{children:f,...p}=l,h=U.Children.toArray(f),y=h.find(hy);if(y){const v=y.props.children,w=h.map(E=>E===y?U.Children.count(v)>1?U.Children.only(null):U.isValidElement(v)?v.props.children:null:E);return _.jsx(o,{...p,ref:a,children:U.isValidElement(v)?U.cloneElement(v,void 0,w):null})}return _.jsx(o,{...p,ref:a,children:f})});return s.displayName=`${i}.Slot`,s}function dy(i){const o=U.forwardRef((s,l)=>{const{children:a,...f}=s;if(U.isValidElement(a)){const p=vy(a),h=my(f,a.props);return a.type!==U.Fragment&&(h.ref=l?Ed(l,p):p),U.cloneElement(a,h)}return U.Children.count(a)>1?U.Children.only(null):null});return o.displayName=`${i}.SlotClone`,o}var py=Symbol("radix.slottable");function hy(i){return U.isValidElement(i)&&typeof i.type=="function"&&"__radixId"in i.type&&i.type.__radixId===py}function my(i,o){const s={...o};for(const l in o){const a=i[l],f=o[l];/^on[A-Z]/.test(l)?a&&f?s[l]=(...h)=>{const y=f(...h);return a(...h),y}:a&&(s[l]=a):l==="style"?s[l]={...a,...f}:l==="className"&&(s[l]=[a,f].filter(Boolean).join(" "))}return{...i,...s}}function vy(i){var l,a;let o=(l=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:l.get,s=o&&"isReactWarning"in o&&o.isReactWarning;return s?i.ref:(o=(a=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:a.get,s=o&&"isReactWarning"in o&&o.isReactWarning,s?i.props.ref:i.props.ref||i.ref)}function yy(i){const o=i+"CollectionProvider",[s,l]=au(o),[a,f]=s(o,{collectionRef:{current:null},itemMap:new Map}),p=A=>{const{scope:N,children:$}=A,q=Ne.useRef(null),z=Ne.useRef(new Map).current;return _.jsx(a,{scope:N,itemMap:z,collectionRef:q,children:$})};p.displayName=o;const h=i+"CollectionSlot",y=tu(h),v=Ne.forwardRef((A,N)=>{const{scope:$,children:q}=A,z=f(h,$),b=Fo(N,z.collectionRef);return _.jsx(y,{ref:b,children:q})});v.displayName=h;const w=i+"CollectionItemSlot",E="data-radix-collection-item",F=tu(w),j=Ne.forwardRef((A,N)=>{const{scope:$,children:q,...z}=A,b=Ne.useRef(null),ie=Fo(N,b),Z=f(w,$);return Ne.useEffect(()=>(Z.itemMap.set(b,{ref:b,...z}),()=>void Z.itemMap.delete(b))),_.jsx(F,{[E]:"",ref:ie,children:q})});j.displayName=w;function T(A){const N=f(i+"CollectionConsumer",A);return Ne.useCallback(()=>{const q=N.collectionRef.current;if(!q)return[];const z=Array.from(q.querySelectorAll(`[${E}]`));return Array.from(N.itemMap.values()).sort((Z,ue)=>z.indexOf(Z.ref.current)-z.indexOf(ue.ref.current))},[N.collectionRef,N.itemMap])}return[{Provider:p,Slot:v,ItemSlot:j},T,l]}var Ao=globalThis!=null&&globalThis.document?U.useLayoutEffect:()=>{},gy=$f[" useId ".trim().toString()]||(()=>{}),wy=0;function kd(i){const[o,s]=U.useState(gy());return Ao(()=>{s(l=>l??String(wy++))},[i]),o?`radix-${o}`:""}var Sy=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],lr=Sy.reduce((i,o)=>{const s=tu(`Primitive.${o}`),l=U.forwardRef((a,f)=>{const{asChild:p,...h}=a,y=p?s:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),_.jsx(y,{...h,ref:f})});return l.displayName=`Primitive.${o}`,{...i,[o]:l}},{});function xy(i){const o=U.useRef(i);return U.useEffect(()=>{o.current=i}),U.useMemo(()=>(...s)=>{var l;return(l=o.current)==null?void 0:l.call(o,...s)},[])}var Cy=$f[" useInsertionEffect ".trim().toString()]||Ao;function Rd({prop:i,defaultProp:o,onChange:s=()=>{},caller:l}){const[a,f,p]=Ey({defaultProp:o,onChange:s}),h=i!==void 0,y=h?i:a;{const w=U.useRef(i!==void 0);U.useEffect(()=>{const E=w.current;E!==h&&console.warn(`${l} is changing from ${E?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),w.current=h},[h,l])}const v=U.useCallback(w=>{var E;if(h){const F=ky(w)?w(i):w;F!==i&&((E=p.current)==null||E.call(p,F))}else f(w)},[h,i,f,p]);return[y,v]}function Ey({defaultProp:i,onChange:o}){const[s,l]=U.useState(i),a=U.useRef(s),f=U.useRef(o);return Cy(()=>{f.current=o},[o]),U.useEffect(()=>{var p;a.current!==s&&((p=f.current)==null||p.call(f,s),a.current=s)},[s,a]),[s,l,f]}function ky(i){return typeof i=="function"}var Ry=U.createContext(void 0);function _d(i){const o=U.useContext(Ry);return i||o||"ltr"}var bl="rovingFocusGroup.onEntryFocus",_y={bubbles:!1,cancelable:!0},ti="RovingFocusGroup",[nu,Od,Oy]=yy(ti),[Py,Pd]=au(ti,[Oy]),[Ny,Ty]=Py(ti),Nd=U.forwardRef((i,o)=>_.jsx(nu.Provider,{scope:i.__scopeRovingFocusGroup,children:_.jsx(nu.Slot,{scope:i.__scopeRovingFocusGroup,children:_.jsx(Fy,{...i,ref:o})})}));Nd.displayName=ti;var Fy=U.forwardRef((i,o)=>{const{__scopeRovingFocusGroup:s,orientation:l,loop:a=!1,dir:f,currentTabStopId:p,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:y,onEntryFocus:v,preventScrollOnEntryFocus:w=!1,...E}=i,F=U.useRef(null),j=Fo(o,F),T=_d(f),[A,N]=Rd({prop:p,defaultProp:h??null,onChange:y,caller:ti}),[$,q]=U.useState(!1),z=xy(v),b=Od(s),ie=U.useRef(!1),[Z,ue]=U.useState(0);return U.useEffect(()=>{const le=F.current;if(le)return le.addEventListener(bl,z),()=>le.removeEventListener(bl,z)},[z]),_.jsx(Ny,{scope:s,orientation:l,dir:T,loop:a,currentTabStopId:A,onItemFocus:U.useCallback(le=>N(le),[N]),onItemShiftTab:U.useCallback(()=>q(!0),[]),onFocusableItemAdd:U.useCallback(()=>ue(le=>le+1),[]),onFocusableItemRemove:U.useCallback(()=>ue(le=>le-1),[]),children:_.jsx(lr.div,{tabIndex:$||Z===0?-1:0,"data-orientation":l,...E,ref:j,style:{outline:"none",...i.style},onMouseDown:qt(i.onMouseDown,()=>{ie.current=!0}),onFocus:qt(i.onFocus,le=>{const De=!ie.current;if(le.target===le.currentTarget&&De&&!$){const ye=new CustomEvent(bl,_y);if(le.currentTarget.dispatchEvent(ye),!ye.defaultPrevented){const Qe=b().filter(ae=>ae.focusable),Te=Qe.find(ae=>ae.active),Se=Qe.find(ae=>ae.id===A),Fe=[Te,Se,...Qe].filter(Boolean).map(ae=>ae.ref.current);Ad(Fe,w)}}ie.current=!1}),onBlur:qt(i.onBlur,()=>q(!1))})})}),Td="RovingFocusGroupItem",Fd=U.forwardRef((i,o)=>{const{__scopeRovingFocusGroup:s,focusable:l=!0,active:a=!1,tabStopId:f,children:p,...h}=i,y=kd(),v=f||y,w=Ty(Td,s),E=w.currentTabStopId===v,F=Od(s),{onFocusableItemAdd:j,onFocusableItemRemove:T,currentTabStopId:A}=w;return U.useEffect(()=>{if(l)return j(),()=>T()},[l,j,T]),_.jsx(nu.ItemSlot,{scope:s,id:v,focusable:l,active:a,children:_.jsx(lr.span,{tabIndex:E?0:-1,"data-orientation":w.orientation,...h,ref:o,onMouseDown:qt(i.onMouseDown,N=>{l?w.onItemFocus(v):N.preventDefault()}),onFocus:qt(i.onFocus,()=>w.onItemFocus(v)),onKeyDown:qt(i.onKeyDown,N=>{if(N.key==="Tab"&&N.shiftKey){w.onItemShiftTab();return}if(N.target!==N.currentTarget)return;const $=Iy(N,w.orientation,w.dir);if($!==void 0){if(N.metaKey||N.ctrlKey||N.altKey||N.shiftKey)return;N.preventDefault();let z=F().filter(b=>b.focusable).map(b=>b.ref.current);if($==="last")z.reverse();else if($==="prev"||$==="next"){$==="prev"&&z.reverse();const b=z.indexOf(N.currentTarget);z=w.loop?Ly(z,b+1):z.slice(b+1)}setTimeout(()=>Ad(z))}}),children:typeof p=="function"?p({isCurrentTabStop:E,hasTabStop:A!=null}):p})})});Fd.displayName=Td;var Ay={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function jy(i,o){return o!=="rtl"?i:i==="ArrowLeft"?"ArrowRight":i==="ArrowRight"?"ArrowLeft":i}function Iy(i,o,s){const l=jy(i.key,s);if(!(o==="vertical"&&["ArrowLeft","ArrowRight"].includes(l))&&!(o==="horizontal"&&["ArrowUp","ArrowDown"].includes(l)))return Ay[l]}function Ad(i,o=!1){const s=document.activeElement;for(const l of i)if(l===s||(l.focus({preventScroll:o}),document.activeElement!==s))return}function Ly(i,o){return i.map((s,l)=>i[(o+l)%i.length])}var Dy=Nd,My=Fd;function Uy(i,o){return U.useReducer((s,l)=>o[s][l]??s,i)}var jd=i=>{const{present:o,children:s}=i,l=zy(o),a=typeof s=="function"?s({present:l.isPresent}):U.Children.only(s),f=Fo(l.ref,Qy(a));return typeof s=="function"||l.isPresent?U.cloneElement(a,{ref:f}):null};jd.displayName="Presence";function zy(i){const[o,s]=U.useState(),l=U.useRef(null),a=U.useRef(i),f=U.useRef("none"),p=i?"mounted":"unmounted",[h,y]=Uy(p,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return U.useEffect(()=>{const v=ho(l.current);f.current=h==="mounted"?v:"none"},[h]),Ao(()=>{const v=l.current,w=a.current;if(w!==i){const F=f.current,j=ho(v);i?y("MOUNT"):j==="none"||(v==null?void 0:v.display)==="none"?y("UNMOUNT"):y(w&&F!==j?"ANIMATION_OUT":"UNMOUNT"),a.current=i}},[i,y]),Ao(()=>{if(o){let v;const w=o.ownerDocument.defaultView??window,E=j=>{const A=ho(l.current).includes(j.animationName);if(j.target===o&&A&&(y("ANIMATION_END"),!a.current)){const N=o.style.animationFillMode;o.style.animationFillMode="forwards",v=w.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=N)})}},F=j=>{j.target===o&&(f.current=ho(l.current))};return o.addEventListener("animationstart",F),o.addEventListener("animationcancel",E),o.addEventListener("animationend",E),()=>{w.clearTimeout(v),o.removeEventListener("animationstart",F),o.removeEventListener("animationcancel",E),o.removeEventListener("animationend",E)}}else y("ANIMATION_END")},[o,y]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:U.useCallback(v=>{l.current=v?getComputedStyle(v):null,s(v)},[])}}function ho(i){return(i==null?void 0:i.animationName)||"none"}function Qy(i){var l,a;let o=(l=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:l.get,s=o&&"isReactWarning"in o&&o.isReactWarning;return s?i.ref:(o=(a=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:a.get,s=o&&"isReactWarning"in o&&o.isReactWarning,s?i.props.ref:i.props.ref||i.ref)}var $o="Tabs",[$y,vg]=au($o,[Pd]),Id=Pd(),[By,cu]=$y($o),Ld=U.forwardRef((i,o)=>{const{__scopeTabs:s,value:l,onValueChange:a,defaultValue:f,orientation:p="horizontal",dir:h,activationMode:y="automatic",...v}=i,w=_d(h),[E,F]=Rd({prop:l,onChange:a,defaultProp:f??"",caller:$o});return _.jsx(By,{scope:s,baseId:kd(),value:E,onValueChange:F,orientation:p,dir:w,activationMode:y,children:_.jsx(lr.div,{dir:w,"data-orientation":p,...v,ref:o})})});Ld.displayName=$o;var Dd="TabsList",Md=U.forwardRef((i,o)=>{const{__scopeTabs:s,loop:l=!0,...a}=i,f=cu(Dd,s),p=Id(s);return _.jsx(Dy,{asChild:!0,...p,orientation:f.orientation,dir:f.dir,loop:l,children:_.jsx(lr.div,{role:"tablist","aria-orientation":f.orientation,...a,ref:o})})});Md.displayName=Dd;var Ud="TabsTrigger",zd=U.forwardRef((i,o)=>{const{__scopeTabs:s,value:l,disabled:a=!1,...f}=i,p=cu(Ud,s),h=Id(s),y=Bd(p.baseId,l),v=qd(p.baseId,l),w=l===p.value;return _.jsx(My,{asChild:!0,...h,focusable:!a,active:w,children:_.jsx(lr.button,{type:"button",role:"tab","aria-selected":w,"aria-controls":v,"data-state":w?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:y,...f,ref:o,onMouseDown:qt(i.onMouseDown,E=>{!a&&E.button===0&&E.ctrlKey===!1?p.onValueChange(l):E.preventDefault()}),onKeyDown:qt(i.onKeyDown,E=>{[" ","Enter"].includes(E.key)&&p.onValueChange(l)}),onFocus:qt(i.onFocus,()=>{const E=p.activationMode!=="manual";!w&&!a&&E&&p.onValueChange(l)})})})});zd.displayName=Ud;var Qd="TabsContent",$d=U.forwardRef((i,o)=>{const{__scopeTabs:s,value:l,forceMount:a,children:f,...p}=i,h=cu(Qd,s),y=Bd(h.baseId,l),v=qd(h.baseId,l),w=l===h.value,E=U.useRef(w);return U.useEffect(()=>{const F=requestAnimationFrame(()=>E.current=!1);return()=>cancelAnimationFrame(F)},[]),_.jsx(jd,{present:a||w,children:({present:F})=>_.jsx(lr.div,{"data-state":w?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":y,hidden:!F,id:v,tabIndex:0,...p,ref:o,style:{...i.style,animationDuration:E.current?"0s":void 0},children:F&&f})})});$d.displayName=Qd;function Bd(i,o){return`${i}-trigger-${o}`}function qd(i,o){return`${i}-content-${o}`}var qy=Ld,bd=Md,Vd=zd,Hd=$d;const by=qy,Kd=U.forwardRef(({className:i,...o},s)=>_.jsx(bd,{ref:s,className:It("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",i),...o}));Kd.displayName=bd.displayName;const xo=U.forwardRef(({className:i,...o},s)=>_.jsx(Vd,{ref:s,className:It("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",i),...o}));xo.displayName=Vd.displayName;const Co=U.forwardRef(({className:i,...o},s)=>_.jsx(Hd,{ref:s,className:It("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...o}));Co.displayName=Hd.displayName;const Bo="/api",Vy=async()=>(await Re.get(`${Bo}/questions`)).data,Hy=async i=>(await Re.get(`${Bo}/questions/category/${i}`)).data,Ky=async i=>(await Re.get(`${Bo}/questions/simulado/${i}`)).data,zf=async i=>(await Re.get(`${Bo}/questions/random/${i}`)).data;function Wy(){var Q,X,B,x;const[i,o]=U.useState("inicio"),[s,l]=U.useState([]),[a,f]=U.useState(0),[p,h]=U.useState([]),[y,v]=U.useState(!1),[w,E]=U.useState(null),[F,j]=U.useState(null),[T,A]=U.useState(null),[N,$]=U.useState(!1),[q,z]=U.useState(null),{data:b,isLoading:ie,error:Z}=Fm("dados",Vy,{staleTime:3e5,retry:3});U.useEffect(()=>{let k=null;return T!==null&&T>0&&i==="simulado"&&!y&&(k=setInterval(()=>{A(K=>K===null||K<=1?(k&&clearInterval(k),v(!0),0):K-1)},1e3)),()=>{k&&clearInterval(k)}},[T,i,y]);const ue=async()=>{try{const k=await zf(40);l(k),h(new Array(k.length).fill(-1)),f(0),v(!1),A(60*60),$(!1),o("simulado")}catch(k){console.error("Erro ao iniciar simulado completo:",k)}},le=async(k,K=10)=>{try{const re=(await Hy(k)).sort(()=>.5-Math.random()).slice(0,K);E(k),l(re),h(new Array(re.length).fill(-1)),f(0),v(!1),A(null),$(!0),o("simulado")}catch(W){console.error("Erro ao iniciar simulado por categoria:",W)}},De=async k=>{try{const K=await Ky(k);j(k),l(K),h(new Array(K.length).fill(-1)),f(0),v(!1),A(null),$(!1),o("simulado")}catch(K){console.error("Erro ao iniciar simulado original:",K)}},ye=async()=>{try{const k=await zf(5);l(k),h(new Array(k.length).fill(-1)),f(0),v(!1),A(null),$(!0),o("simulado")}catch(k){console.error("Erro ao iniciar modo rápido:",k)}},Qe=k=>{if(p[a]!==-1&&!N)return;const K=[...p];K[a]=k,h(K),N&&(z(k),setTimeout(()=>{z(null),a<s.length-1?f(a+1):v(!0)},1500))},Te=()=>{a<s.length-1?f(a+1):v(!0)},Se=()=>{a>0&&f(a-1)},_e=()=>{v(!0)},Fe=()=>{o("inicio"),l([]),h([]),f(0),v(!1),E(null),j(null),A(null)},ae=()=>{if(!s.length)return{acertos:0,total:0,percentual:0};const k=p.reduce((K,W,re)=>W===s[re].resposta_correta?K+1:K,0);return{acertos:k,total:s.length,percentual:Math.round(k/s.length*100)}};if(ie)return _.jsx("div",{className:"flex items-center justify-center min-h-screen",children:_.jsxs("div",{className:"text-center",children:[_.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Carregando..."}),_.jsx("p",{children:"Preparando seu simulado ITIL"})]})});if(Z)return _.jsx("div",{className:"flex items-center justify-center min-h-screen",children:_.jsxs("div",{className:"text-center",children:[_.jsx("h2",{className:"text-2xl font-bold mb-4 text-red-600",children:"Erro"}),_.jsx("p",{children:Z.message}),_.jsx(_t,{onClick:()=>window.location.reload(),className:"mt-4",children:"Tentar Novamente"})]})});const ce={categorias:[{id:"conceitos_fundamentais",nome:"Conceitos Fundamentais do ITIL",descricao:"Definições básicas, Sistema de Valor de Serviço e Cadeia de Valor de Serviço",subcategorias:["definicoes_basicas","sistema_valor_servico"]},{id:"principios_orientadores",nome:"Princípios Orientadores",descricao:"Os 7 princípios orientadores do ITIL 4",subcategorias:["foco_valor","colaborar_promover_visibilidade"]},{id:"praticas_gerenciamento",nome:"Práticas de Gerenciamento",descricao:"Central de serviço, Gerenciamento de incidente, problema, requisição, etc.",subcategorias:["central_servico","gerenciamento_nivel_servico"]}],simulados:[{id:"ITIL_4_DUMP_6_6",nome:"ITIL 4 DUMP 6/6",total_questoes:40,descricao:"Simulado focado em conceitos fundamentais do ITIL 4"},{id:"ITIL_4_DUMP_5_6",nome:"ITIL 4 DUMP 5/6",total_questoes:40,descricao:"Simulado focado em práticas de gerenciamento ITIL 4"}]};if(i==="inicio")return _.jsxs("div",{className:"container mx-auto px-4 py-8",children:[_.jsxs("header",{className:"text-center mb-12",children:[_.jsx("h1",{className:"text-4xl font-bold mb-2",children:"Simulado ITIL 4"}),_.jsx("p",{className:"text-xl",children:"Prepare-se para sua certificação ITIL com nossos simulados interativos"})]}),_.jsxs(by,{defaultValue:"modos",className:"max-w-4xl mx-auto",children:[_.jsxs(Kd,{className:"grid grid-cols-3 mb-8",children:[_.jsx(xo,{value:"modos",children:"Modos de Estudo"}),_.jsx(xo,{value:"categorias",children:"Categorias"}),_.jsx(xo,{value:"simulados",children:"Simulados Originais"})]}),_.jsx(Co,{value:"modos",children:_.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[_.jsxs(cn,{children:[_.jsxs(fn,{children:[_.jsx(dn,{children:"Simulado Completo"}),_.jsx(Pn,{children:"Simule a experiência real do exame ITIL"})]}),_.jsx(pn,{children:_.jsx("p",{children:"40 questões aleatórias com tempo limite de 60 minutos. Feedback apenas ao final."})}),_.jsx(Nn,{children:_.jsx(_t,{onClick:ue,children:"Iniciar"})})]}),_.jsxs(cn,{children:[_.jsxs(fn,{children:[_.jsx(dn,{children:"Modo Rápido"}),_.jsx(Pn,{children:"Revisão rápida com feedback imediato"})]}),_.jsx(pn,{children:_.jsx("p",{children:"5 questões aleatórias com feedback após cada resposta. Ideal para revisões rápidas."})}),_.jsx(Nn,{children:_.jsx(_t,{onClick:ye,children:"Iniciar"})})]})]})}),_.jsx(Co,{value:"categorias",children:_.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:((b==null?void 0:b.categorias)||ce.categorias).map(k=>_.jsxs(cn,{children:[_.jsxs(fn,{children:[_.jsx(dn,{children:k.nome}),_.jsx(Pn,{children:k.descricao})]}),_.jsx(pn,{children:_.jsxs("p",{children:["Estude questões específicas sobre ",k.nome.toLowerCase(),"."]})}),_.jsx(Nn,{children:_.jsx(_t,{onClick:()=>le(k.id),children:"Iniciar (10 questões)"})})]},k.id))})}),_.jsx(Co,{value:"simulados",children:_.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:((b==null?void 0:b.simulados)||ce.simulados).map(k=>_.jsxs(cn,{children:[_.jsxs(fn,{children:[_.jsx(dn,{children:k.nome}),_.jsx(Pn,{children:k.descricao})]}),_.jsx(pn,{children:_.jsxs("p",{children:[k.total_questoes," questões no formato original."]})}),_.jsx(Nn,{children:_.jsx(_t,{onClick:()=>De(k.id),children:"Iniciar"})})]},k.id))})})]})]});if(i==="simulado"&&!y){const k=s[a];return _.jsxs("div",{className:"container mx-auto px-4 py-8 text-foreground",children:[_.jsxs("header",{className:"mb-8",children:[_.jsxs("div",{className:"flex justify-between items-center mb-4",children:[_.jsx(_t,{variant:"outline",onClick:Fe,children:"Voltar"}),_.jsxs("div",{className:"text-center",children:[_.jsx("h2",{className:"text-xl font-bold",children:w?(Q=((b==null?void 0:b.categorias)||ce.categorias).find(K=>K.id===w))==null?void 0:Q.nome:F?(X=((b==null?void 0:b.simulados)||ce.simulados).find(K=>K.id===F))==null?void 0:X.nome:"Simulado ITIL 4"}),T&&_.jsxs("p",{className:"text-sm",children:["Tempo restante: ",Math.floor(T/60),":",(T%60).toString().padStart(2,"0")]})]}),_.jsx(_t,{variant:"destructive",onClick:_e,children:"Finalizar"})]}),_.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 h-2 rounded-full",children:_.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${(a+1)/s.length*100}%`}})}),_.jsxs("p",{className:"text-center mt-2",children:["Questão ",a+1," de ",s.length]})]}),_.jsxs(cn,{className:"mb-8",children:[_.jsxs(fn,{children:[_.jsx(dn,{className:"text-foreground",children:k.pergunta}),k.dificuldade&&_.jsxs(Pn,{className:"text-muted-foreground",children:["Dificuldade: ",k.dificuldade==="facil"?"Fácil":k.dificuldade==="media"?"Média":"Difícil"]})]}),_.jsx(pn,{children:_.jsx("div",{className:"space-y-4",children:k.alternativas.map((K,W)=>_.jsx("div",{className:`p-4 border rounded-lg cursor-pointer transition-colors text-foreground
                    ${p[a]===W?"border-blue-500 bg-blue-50 dark:bg-blue-900 dark:border-blue-400":"hover:bg-gray-50 dark:hover:bg-gray-800"}
                    ${N&&q===W?W===k.resposta_correta?"border-green-500 bg-green-50 dark:bg-green-900 dark:border-green-400":"border-red-500 bg-red-50 dark:bg-red-900 dark:border-red-400":""}`,onClick:()=>Qe(W),children:_.jsxs("div",{className:"flex items-start",children:[_.jsx("div",{className:"flex-shrink-0 w-6 h-6 rounded-full border flex items-center justify-center mr-3 text-foreground",children:String.fromCharCode(65+W)}),_.jsx("div",{className:"text-foreground",children:K})]})},W))})}),_.jsxs(Nn,{className:"flex justify-between",children:[_.jsx(_t,{variant:"outline",onClick:Se,disabled:a===0,children:"Anterior"}),_.jsx(_t,{onClick:Te,disabled:p[a]===-1||N&&q!==null,children:a===s.length-1?"Finalizar":"Próxima"})]})]}),N&&q!==null&&_.jsxs(cn,{className:`mb-4 ${q===k.resposta_correta?"border-green-500 dark:border-green-400":"border-red-500 dark:border-red-400"}`,children:[_.jsx(fn,{children:_.jsx(dn,{className:q===k.resposta_correta?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",children:q===k.resposta_correta?"Correto!":"Incorreto!"})}),_.jsxs(pn,{children:[_.jsx("p",{className:"mb-2 text-foreground",children:q!==k.resposta_correta&&_.jsxs("span",{children:["A resposta correta é: ",_.jsx("strong",{children:String.fromCharCode(65+k.resposta_correta)})]})}),_.jsx("p",{className:"text-foreground",children:k.explicacao})]})]})]})}if(i==="simulado"&&y){const k=ae(),K=k.percentual>=70;return _.jsx("div",{className:"container mx-auto px-4 py-8",children:_.jsxs(cn,{className:"max-w-2xl mx-auto",children:[_.jsxs(fn,{children:[_.jsx(dn,{className:"text-center text-2xl text-foreground",children:"Resultado do Simulado"}),_.jsx(Pn,{className:"text-center text-muted-foreground",children:w?(B=((b==null?void 0:b.categorias)||ce.categorias).find(W=>W.id===w))==null?void 0:B.nome:F?(x=((b==null?void 0:b.simulados)||ce.simulados).find(W=>W.id===F))==null?void 0:x.nome:"Simulado ITIL 4"})]}),_.jsxs(pn,{children:[_.jsxs("div",{className:"text-center mb-8",children:[_.jsxs("div",{className:`text-5xl font-bold mb-2 ${K?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[k.percentual,"%"]}),_.jsx("p",{className:"text-xl mb-4 text-foreground",children:K?"Aprovado!":"Não aprovado"}),_.jsxs("p",{className:"text-foreground",children:["Você acertou ",_.jsx("strong",{children:k.acertos})," de ",_.jsx("strong",{children:k.total})," questões."]})]}),_.jsxs("div",{className:"space-y-6",children:[_.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Revisão das Questões:"}),s.map((W,re)=>_.jsxs("div",{className:"border rounded-lg p-4 dark:border-gray-700",children:[_.jsxs("div",{className:"flex items-start mb-2",children:[_.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${p[re]===W.resposta_correta?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:p[re]===W.resposta_correta?"✓":"✗"}),_.jsx("div",{className:"font-medium text-foreground",children:W.pergunta})]}),_.jsxs("div",{className:"ml-8 mb-2",children:[_.jsxs("p",{className:"text-foreground",children:["Sua resposta: ",_.jsxs("span",{className:p[re]===W.resposta_correta?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400",children:[String.fromCharCode(65+p[re]),". ",W.alternativas[p[re]]]})]}),p[re]!==W.resposta_correta&&_.jsxs("p",{className:"text-green-600 dark:text-green-400",children:["Resposta correta: ",String.fromCharCode(65+W.resposta_correta),". ",W.alternativas[W.resposta_correta]]})]}),_.jsx("div",{className:"ml-8 text-sm text-gray-600 dark:text-gray-300",children:_.jsx("p",{children:W.explicacao})})]},re))]})]}),_.jsx(Nn,{className:"flex justify-center",children:_.jsx(_t,{onClick:Fe,children:"Voltar ao Início"})})]})})}return null}const Gy=new ym({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:1,staleTime:3e5}}}),Jy=document.getElementById("root"),Xy=Yh.createRoot(Jy);Xy.render(_.jsx(Ne.StrictMode,{children:_.jsx(Rm,{client:Gy,children:_.jsx(Wy,{})})}));
