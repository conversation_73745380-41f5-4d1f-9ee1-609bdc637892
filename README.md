# Simulado ITIL 4 - Aplicativo de Estudo

Este é um aplicativo web interativo para estudar para a certificação ITIL 4, desenvolvido com React (frontend) e Flask (backend).

## Características

- **Diferentes modos de estudo:**
  - <PERSON><PERSON><PERSON><PERSON> (40 questões, tempo limitado)
  - Simulad<PERSON> por Categoria (foco em tópicos específicos)
  - Simulado Original (reproduzindo os testes originais)
  - <PERSON><PERSON> (5-10 questões com feedback imediato)

- **Recursos de aprendizado:**
  - Feedback detalhado para cada questão
  - Estatísticas de desempenho
  - Sistema de revisão
  - Explicações detalhadas

## Estrutura do Projeto

```
itil-app/
├── frontend/             # Código fonte do frontend React
│   ├── src/              # Arquivos fonte
│   ├── dist/             # Build compilada
│   └── package.json      # Dependências
│
└── backend/              # Código fonte do backend Flask
    ├── src/              # Arquivos fonte
    │   ├── main.py       # Ponto de entrada
    │   ├── data/         # Dados das questões
    │   └── static/       # Arquivos estáticos (frontend compilado)
    ├── venv/             # Ambiente virtual Python
    └── requirements.txt  # Dependências Python
```

## Requisitos

### Frontend
- Node.js 16+
- npm ou pnpm

### Backend
- Python 3.8+
- pip

## Instalação

### Frontend

```bash
cd frontend
npm install
# ou
pnpm install
```

### Backend

```bash
cd backend
python -m venv venv
source venv/bin/activate  # No Windows: venv\Scripts\activate
pip install -r requirements.txt
```

## Execução

### Desenvolvimento

1. **Frontend:**
```bash
cd frontend
npm run dev
# ou
pnpm run dev
```

2. **Backend:**
```bash
cd backend
source venv/bin/activate  # No Windows: venv\Scripts\activate
python src/main.py
```

### Produção

1. **Compilar o frontend:**
```bash
cd frontend
npm run build
# ou
pnpm run build
```

2. **Copiar arquivos para o backend:**
```bash
cp -r frontend/dist/* backend/src/static/
```

3. **Executar o backend:**
```bash
cd backend
source venv/bin/activate  # No Windows: venv\Scripts\activate
python src/main.py
```

## Dados

O aplicativo contém questões extraídas de simulados ITIL 4, organizadas por categorias e tópicos. Os dados estão armazenados em formato JSON no diretório `backend/src/data/`.

## Personalização

Você pode personalizar o aplicativo modificando:

- **Questões:** Edite o arquivo `backend/src/data/questions.json`
- **Categorias:** Modifique as categorias no mesmo arquivo JSON
- **Aparência:** Edite os arquivos CSS em `frontend/src/`

## Licença

Este projeto é fornecido para uso pessoal e educacional.

## Contato

Para sugestões ou dúvidas, entre em contato através do GitHub.
