import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Target, 
  Clock, 
  Zap, 
  BookOpen, 
  Globe,
  ChevronRight,
  Award,
  TrendingUp
} from 'lucide-react';
import Breadcrumb from '../Layout/Breadcrumb';

interface PracticeSectionProps {
  onSectionChange: (section: string) => void;
  idiomas?: Array<{code: string, name: string, count: number}>;
  dados?: any;
  iniciarSimuladoCompleto: () => void;
  iniciarModoRapido: () => void;
  iniciarSimuladoPorCategoria: (categoria: string, numQuestoes?: number) => void;
  setIdiomaSelecionado: (idioma: string) => void;
}

const PracticeSection: React.FC<PracticeSectionProps> = ({
  onSectionChang<PERSON>,
  idiomas,
  dados,
  iniciarSimuladoCompleto,
  iniciarModoRapido,
  iniciarSimuladoPorCategoria,
  setIdiomaSelecionado
}) => {
  const breadcrumbItems = [
    { label: 'Simulados', active: true }
  ];

  const practiceStats = [
    { label: 'Questões Disponíveis', value: '128+', icon: Target },
    { label: 'Categorias', value: '5', icon: BookOpen },
    { label: 'Idiomas', value: idiomas?.length || '2', icon: Globe },
    { label: 'Taxa de Sucesso', value: '85%', icon: Award }
  ];

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} onSectionChange={onSectionChange} />
      
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Simulados ITIL 4
        </h1>
        <p className="text-lg text-gray-600">
          Pratique com simulados que replicam a experiência real do exame
        </p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {practiceStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <Icon className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Tabs defaultValue="modos" className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="modos">Modos de Estudo</TabsTrigger>
          <TabsTrigger value="categorias">Por Categoria</TabsTrigger>
          <TabsTrigger value="simulados">Simulados Originais</TabsTrigger>
        </TabsList>

        {/* Modos de Estudo */}
        <TabsContent value="modos">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Simulado Completo */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2 text-blue-600" />
                  Simulado Completo
                </CardTitle>
                <CardDescription>
                  Experiência real do exame ITIL 4
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Target className="w-4 h-4 mr-2" />
                    40 questões aleatórias
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-2" />
                    60 minutos de duração
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Award className="w-4 h-4 mr-2" />
                    Feedback ao final
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full"
                  onClick={() => {
                    setIdiomaSelecionado('pt-br');
                    iniciarSimuladoCompleto();
                  }}
                >
                  Iniciar Simulado
                </Button>
              </CardFooter>
            </Card>

            {/* Modo Rápido */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-yellow-600" />
                  Modo Rápido
                </CardTitle>
                <CardDescription>
                  Revisão rápida com feedback imediato
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Target className="w-4 h-4 mr-2" />
                    5 questões aleatórias
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-2" />
                    Sem limite de tempo
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Feedback imediato
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full"
                  onClick={() => {
                    setIdiomaSelecionado('pt-br');
                    iniciarModoRapido();
                  }}
                >
                  Iniciar Modo Rápido
                </Button>
              </CardFooter>
            </Card>

            {/* Simulados por Idioma */}
            {idiomas && idiomas.map((idioma) => (
              <Card key={`simulado-${idioma.code}`} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Globe className="w-5 h-5 mr-2 text-green-600" />
                    {idioma.code === 'pt-br' ? 'Simulado em Português' :
                     idioma.code === 'en' ? 'Simulado em Inglês' :
                     idioma.code === 'es' ? 'Simulado em Espanhol' :
                     `Simulado em ${idioma.name}`}
                  </CardTitle>
                  <CardDescription>
                    Questões ITIL 4 em {idioma.name}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-600">
                      <Target className="w-4 h-4 mr-2" />
                      {idioma.count} questões disponíveis
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Globe className="w-4 h-4 mr-2" />
                      Idioma: {idioma.name}
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <div className="flex gap-2 w-full">
                    <Button 
                      size="sm"
                      className="flex-1"
                      onClick={() => {
                        setIdiomaSelecionado(idioma.code);
                        iniciarModoRapido();
                      }}
                    >
                      Rápido (5)
                    </Button>
                    {idioma.count >= 40 && (
                      <Button 
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          setIdiomaSelecionado(idioma.code);
                          iniciarSimuladoCompleto();
                        }}
                      >
                        Completo (40)
                      </Button>
                    )}
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Por Categoria */}
        <TabsContent value="categorias">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {dados?.categorias?.map((categoria: any) => (
              <Card key={categoria.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{categoria.nome}</span>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </CardTitle>
                  <CardDescription>{categoria.descricao}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Pratique questões específicas desta categoria para focar seus estudos.
                  </p>
                  <div className="flex items-center text-sm text-gray-500">
                    <Target className="w-4 h-4 mr-2" />
                    10 questões por simulado
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full"
                    onClick={() => iniciarSimuladoPorCategoria(categoria.id, 10)}
                  >
                    Praticar Categoria
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Simulados Originais */}
        <TabsContent value="simulados">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {dados?.simulados?.map((simulado: any) => (
              <Card key={simulado.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{simulado.nome}</span>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </CardTitle>
                  <CardDescription>{simulado.descricao}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Target className="w-4 h-4 mr-2" />
                      {simulado.total_questoes} questões
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <BookOpen className="w-4 h-4 mr-2" />
                      Fonte: {simulado.id.includes('DAYPO') ? 'Daypo' : 'Original'}
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full"
                    onClick={() => {
                      // Implementar iniciarSimuladoOriginal se necessário
                      console.log('Iniciar simulado original:', simulado.id);
                    }}
                  >
                    Iniciar Simulado
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="text-blue-800">Dica de Estudo</CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <p className="mb-4">
            Para melhor preparação, recomendamos começar com simulados por categoria 
            para identificar pontos fracos, depois praticar com o modo rápido para 
            revisão, e finalmente fazer simulados completos para simular o exame real.
          </p>
          <div className="flex flex-wrap gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => onSectionChange('tips')}
            >
              Ver Dicas de Estudo
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              className="border-blue-300 text-blue-700 hover:bg-blue-100"
              onClick={() => onSectionChange('knowledge')}
            >
              Base de Conhecimento
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PracticeSection;
